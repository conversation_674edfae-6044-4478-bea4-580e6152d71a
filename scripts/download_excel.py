import http.client
import json
import os
import string
import pathlib
from pathlib import Path  # 添加这一行
import time  # 导入 time 模块

# user-access-token
ACCESS_TOKEN = "u-euWTDepzp0cVEE1pNMDUIM5kigj014GVUq20lhm00xmK"  # 调用get_access_token重新赋值
#
USE_Tenant_access_token = True  # TODO   # 使用应用的TOKEN，通过APPSECRET 动态获取 。fasle使用USER_ACCESS_TOKEN，需要手动配置ACCESS_TOKEN

CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))

# 目标文件放置文件夹
DIR_PATH = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/lite/assets/")
DIR_PATH_ENGLISH = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/lite/en")
DIR_PATH_JA = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/lite/ja")
DIR_PATH_KO = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/lite/ko")
DIR_PATH_DE = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/lite/de")
DIR_PATH_ES = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/lite/es")
DIR_PATH_ID = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/lite/id")
DIR_PATH_PT = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/lite/pt")

# 压缩后的文件名
COMPRESS_FILE = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/assets/impossible.mp3")
COMPRESS_FILE_ENGLISH = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/assets/en_impossible.mp3")
COMPRESS_FILE_JA = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/assets/ja_impossible.mp3")
COMPRESS_FILE_KO = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/assets/ko_impossible.mp3")
COMPRESS_FILE_DE = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/assets/de_impossible.mp3")
COMPRESS_FILE_ES = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/assets/es_impossible.mp3")
COMPRESS_FILE_ID = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/assets/id_impossible.mp3")
COMPRESS_FILE_PT = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/assets/pt_impossible.mp3")


APP_ID = "cli_a465fa21177a100e"
APP_SECRET = "YV1kSvzjwaOtvhw1mlwkycPMH5wJdpod"


class SimpleSheetInfo:

    def __init__(self, row_count: str, column_count: str, sheet_id: str, title: str):
        """
        :param row_count: 行数
        :param column_count: 列数
        :param sheet_id: sheet_id
        :param title: sheet的title
        """
        self.column_count = column_count
        self.row_count = row_count
        self.sheet_id = sheet_id
        self.title = title


class Spreadsheets:
    simple_sheet_info: SimpleSheetInfo = None

    def __init__(self, spreadsheet_token: str, title: str, saved_file_name: str):
        """
        :param title 表格文件名称
        :param spreadsheet_token:  飞书表格的标识 例如：R0PMsS9TRhFYhSt0iQ5ciz0xnGc
        :param saved_file_name: 保存后的文件名，例如test.txt
        """
        self.title = title
        self.spreadsheet_token = spreadsheet_token
        self.saved_file_name = saved_file_name


def get_access_token():
    """
        用于获取应用得tenant_access_token
    :return:
    """
    url = "/open-apis/auth/v3/app_access_token/internal/"
    headers = {
        'Content-Type': 'application/json; charset=utf-8'
    }
    payload = {
        'app_id': APP_ID,
        'app_secret': APP_SECRET
    }
    conn = http.client.HTTPSConnection("open.feishu.cn")

    # payload = ''
    conn.request("POST", url, json.dumps(payload), headers)
    res = conn.getresponse()
    data = res.read()
    global ACCESS_TOKEN
    ACCESS_TOKEN = json.loads(data)['tenant_access_token']
    print(ACCESS_TOKEN)


# https://open.feishu.cn/document/server-docs/docs/faq  7.2节获取知识空间中的所有sheet文档。

def get_wiki_spaces():
    """
       获取知识空间列表
    :return:
    """
    conn = http.client.HTTPSConnection("open.feishu.cn")

    payload = ''

    headers = {'Authorization': f'Bearer {ACCESS_TOKEN}'}
    query_url = f"/open-apis/wiki/v2/spaces"

    conn.request("GET", query_url, payload, headers)
    res = conn.getresponse()
    if res.status != 200:
        print("[-]Error", res.read())
    else:
        data = res.read()
        data_in_sheet = json.loads(data.decode("utf-8"))['data']['items']
        # print(data_in_sheet)
        print("知识空间列表:")
        for item in data_in_sheet:
            print(f"name ={item['name']},space_id={item['space_id']}")
    conn.close()


def get_space_nodes(space_id: str, parent_node_token: str = None):
    """
    获取知识空间子节点列表  /open-apis/wiki/v2/spaces/:space_id/nodes
    :return:
    """
    conn = http.client.HTTPSConnection("open.feishu.cn")
    payload = ''
    headers = {'Authorization': f'Bearer {ACCESS_TOKEN}'}
    query_url = f"/open-apis/wiki/v2/spaces/{space_id}/nodes?page_size=50"

    if parent_node_token is not None:
        query_url += f"&parent_node_token={parent_node_token}"

    conn.request("GET", query_url, payload, headers)

    res = conn.getresponse()
    if res.status != 200:
        print("[-]Error", res.read())
    else:
        data = res.read()
        data_in_sheet = json.loads(data.decode("utf-8"))['data']['items']
        print("知识空间子节点列表:")
        for item in data_in_sheet:
            print(f"node_token={item['node_token']},obj_token={item['obj_token']}"
                  f",obj_type={item['obj_type']},space_id={item['space_id']},title={item['title']}")
        return data_in_sheet
    conn.close()


def get_all_sheet_info(spreadsheet_token: str) -> []:
    # /open-apis/sheets/v3/spreadsheets/:spreadsheet_token/sheets/query
    conn = http.client.HTTPSConnection("open.feishu.cn")
    payload = ''
    headers = {'Authorization': f'Bearer {ACCESS_TOKEN}'}
    query_url = f"/open-apis/sheets/v3/spreadsheets/{spreadsheet_token}/sheets/query"

    conn.request("GET", query_url, payload, headers)

    res = conn.getresponse()
    if res.status != 200:
        print("[-]Error", res.read())
    else:
        data = res.read()
        result = []
        for data_in_sheet in json.loads(data.decode("utf-8"))['data']['sheets']:
            sheet_id = data_in_sheet['sheet_id']
            title = data_in_sheet['title']
            row_count = data_in_sheet['grid_properties']['row_count']
            column_count = data_in_sheet['grid_properties']['column_count']
            simple_sheet_info = SimpleSheetInfo(
                column_count=column_count, row_count=row_count, sheet_id=sheet_id, title=title)
            result.append(simple_sheet_info)
        conn.close()
        return result


def get_all_sheet_and_print_class_define_str():
    """
    工具函数，打印所有需要读取的在线excel的类定义，需要手动拷贝，核对需要保存的文件名等。
    7251081850708328449 space_id 帝国人生
    YTH6w8aS6iK4ZTkTv9EcG9eOnkf parent_node_token 配置表节点
    :return:
    """
    # get_space_nodes("7251081850708328449", "YTH6w8aS6iK4ZTkTv9EcG9eOnkf")  # 获取知识空间的子节点 配置表节点
    # 示例结果：
    # node_token=H8v4wQUaYiudYNkCNTVcNFBPnuc,obj_token=IGoXsXG8Vh90dNtjq3hcdWt1nag,obj_type=sheet,space_id=7251081850708328449,title=skill军团卡

    items = get_space_nodes("7251081850708328449", "YTH6w8aS6iK4ZTkTv9EcG9eOnkf")
    for item in items:
        simple_sheet_info = get_all_sheet_info(item['obj_token'])
        s = f"""Spreadsheets(title="{item['title']}",spreadsheet_token="{item['obj_token']}", saved_file_name="{item['title']}.txt"),"""
        print(s)


# 对读取到的行进行数据类型转换等。
def transform(x):
    if x is None:  # 空列
        return ""
    if type(x) == list:  # url 链接的情况
        print("发现特殊数据，正在处理... >>>", x)
        if len(x) > 0 and type(x[0]) == dict and 'text' in x[0]:
            return x[0]['text']
        else:
            raise Exception('发现特殊数据.. 请制定处置规则')
    return str(x)


def get_content_write_to_file(sheet: Spreadsheets, dirPath: Path, skipLines: int, direct: bool()):
    """Fetches and writes spreadsheet data to a file in chunks if rows exceed 10,000."""
    conn = http.client.HTTPSConnection("open.feishu.cn")
    headers = {'Authorization': f'Bearer {ACCESS_TOKEN}'}
    chunk_size = 3000  # Number of rows per fetch
    
    sheet_end_column = int(sheet.simple_sheet_info.column_count)
    if sheet_end_column <= 26:
        sheet_end = string.ascii_uppercase[sheet_end_column - 1]
    else:
        sheet_end = "A" + string.ascii_uppercase[(sheet_end_column - 1) % 26]
    
    total_rows = int(sheet.simple_sheet_info.row_count)
    finalPath = dirPath if direct else dirPath.joinpath(sheet.saved_file_name)

    first_batch = True  # Flag to apply skipLines only for the first batch
    is_first_row = True  # Track if it's the first row being written

    try:
        with open(finalPath, "w", encoding='utf-8') as f:
            for start_row in range(1, total_rows + 1, chunk_size):
                end_row = min(start_row + chunk_size - 1, total_rows)
                sheet_range = f"{sheet.simple_sheet_info.sheet_id}!A{start_row}:{sheet_end}{end_row}"
                
                query_url = (f"/open-apis/sheets/v2/spreadsheets/{sheet.spreadsheet_token}/values_batch_get"
                             f"?valueRenderOption=FormattedValue&ranges={sheet_range}")
                
                conn.request("GET", query_url, '', headers)
                res = conn.getresponse()
                
                if res.status != 200:
                    print(f"{sheet.title} - Failed to fetch content for range {sheet_range}: {res.read()}")
                    continue
                
                data = res.read()
                try:
                    data_in_sheet = json.loads(data.decode("utf-8"))['data']['valueRanges'][0]['values']
                    rows_to_process = data_in_sheet[skipLines if first_batch else 0:]  # Apply skipLines only once
                    for idx, item in enumerate(rows_to_process):
                        if not item[0]:  # Skip empty rows
                            continue
                        item_temp = map(transform, item)  # Apply transform rules
                        row_content = "\t".join(item_temp)
                        if not is_first_row:
                            f.write("\n")  # Write a newline before adding subsequent rows
                        f.write(row_content)
                        is_first_row = False  # After the first write, subsequent rows will have newlines
                    first_batch = False
                except KeyError as e:
                    print(f"KeyError encountered while processing range {sheet_range}: {e}")
                except Exception as e:
                    print(f"Error encountered while processing range {sheet_range}: {e}")
    finally:
        conn.close()


sheet_list = [
    # Spreadsheets(title="示例", spreadsheet_token="QlbYsaMEfhmhihtWKg8cuB0TnZd", saved_file_name="示例.txt"),
]


def compress_dir(dirPath: Path, compressPath: Path):
    p = "58598744820488376"
    try:
        import pyminizip
    except ImportError:
        print("[-]未发现依赖项pyminizip，正在自动安装........")
        os.system("pip install pyminizip")
        import pyminizip
        print("[+]完成安装依赖项pyminizip\n")

    file_path = []
    all_files = os.listdir(dirPath)
    for item in all_files:
        full_item_path = dirPath.joinpath(item)
        if full_item_path.is_file():
            file_path.append(full_item_path.resolve().as_posix())
    pyminizip.compress_multiple(file_path, [], compressPath.resolve().as_posix(), p, 1)
    print(f"[2]打包压缩文件成功:{compressPath.resolve()}")
    pass


def main(obj_token: str, dirPath: Path, compressPath: Path):
    """
        1、获取access_token  get_access_token()
        2、遍历sheet_list 获取最新数据，写入文件
    """

    if USE_Tenant_access_token:
        get_access_token()

    count = 0
    all_sheet_in_excel = get_all_sheet_info(obj_token)
    for item in all_sheet_in_excel:
        t = Spreadsheets(title=item.title, spreadsheet_token=obj_token,
                         saved_file_name=item.title + '.txt')
        t.simple_sheet_info = item
        sheet_list.append(t)
        pass

    for item in sheet_list:

        print(f"downloading  {item.title}.txt")
        # 定制化存储的文件名
        # if item.title == 'skill棋子':
        #     item.title = 'skill_chess'
        #     item.saved_file_name = item.title + '.txt'

        # 定制化列数
        # if item.title == 'map':
        #     item.simple_sheet_info.column_count = 18

        ###
        # end
        try:
            get_content_write_to_file(item, dirPath, 3, False)
        except Exception as e:
            raise (e)
        count += 1
    # 合并skill 相关文件 为 skill.txt ,并且删除下载得文件。
    skill_file = dirPath.joinpath("skill.txt")
    skill_files = ["战斗skill兵种.txt",
                   "战斗skill魔法.txt",
                   "战斗skill地形.txt",
                   "战斗skill专业.txt",
                   "战斗skill特长.txt",
                   "战斗skill装备.txt",
                   "冒险skill专业.txt",
                   "冒险skill特长.txt",
                   "冒险skill装备.txt",
                   "冒险skill天赋.txt",
                   "冒险skill探索.txt",
                   "冒险skill建筑.txt",]
    #
    with open(skill_file, mode='w', encoding='utf-8') as file_write:
        for item in skill_files:
            temp_path = dirPath.joinpath(item)
            with open(temp_path, mode='r', encoding='utf-8') as file_read:
                data = file_read.readlines()
                file_write.writelines(data)
                print("{}  写入skill.txt".format(temp_path.absolute().name))
            file_write.write("\n")

    for item in skill_files:
        temp_path = dirPath.joinpath(item)
        if os.path.exists(temp_path):
            os.remove(temp_path)
            print(temp_path.absolute().name + " 临时文件删除成功！！！")
            
            
     # 合并skill 相关文件 为 skill.txt ,并且删除下载得文件。
    event_file = dirPath.joinpath("event.txt")
    event_files = ["event1.txt",
                    "event2.txt",
                    "event3.txt"]
    #
    with open(event_file, mode='w', encoding='utf-8') as file_write:
        for item in event_files:
            temp_path = dirPath.joinpath(item)
            with open(temp_path, mode='r', encoding='utf-8') as file_read:
                data = file_read.readlines()
                file_write.writelines(data)
                print("{}  写入event.txt".format(temp_path.absolute().name))
            file_write.write("\n")

    for item in event_files:
        temp_path = dirPath.joinpath(item)
        if os.path.exists(temp_path):
            os.remove(temp_path)
            print(temp_path.absolute().name + " 临时文件删除成功！！！")

    print(f"[1]共计{len(sheet_list)}在线配置文件,完成下载更新: {count}个,失败{len(sheet_list) - count}")

    compress_dir(dirPath, compressPath)

def download_string():
    obj_token = "WnNNsTJADhpfj3td8rVch3RCn8b"
    if USE_Tenant_access_token:
        get_access_token()

    count = 0
    all_sheet_in_excel = get_all_sheet_info(obj_token)

    STRING_PATH_CN = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/res/values/strings.xml")
    STRING_PATH_EN = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/res/values-en/strings.xml")
    STRING_PATH_JA = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/res/values-ja/strings.xml")
    STRING_PATH_KO = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/res/values-ko/strings.xml")
    STRING_PATH_DE = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/res/values-de/strings.xml")
    STRING_PATH_ES = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/res/values-es/strings.xml")
    STRING_PATH_ID = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/res/values-in/strings.xml")
    STRING_PATH_PT = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/res/values-pt/strings.xml")
    
    STRING_PATH2_CN = pathlib.Path(CURRENT_DIRECTORY).joinpath("../core/src/main/res/values/strings.xml")
    STRING_PATH2_EN = pathlib.Path(CURRENT_DIRECTORY).joinpath("../core/src/main/res/values-en/strings.xml")
    STRING_PATH2_JA = pathlib.Path(CURRENT_DIRECTORY).joinpath("../core/src/main/res/values-ja/strings.xml")
    STRING_PATH2_KO = pathlib.Path(CURRENT_DIRECTORY).joinpath("../core/src/main/res/values-ko/strings.xml")
    STRING_PATH2_DE = pathlib.Path(CURRENT_DIRECTORY).joinpath("../core/src/main/res/values-de/strings.xml")
    STRING_PATH2_ES = pathlib.Path(CURRENT_DIRECTORY).joinpath("../core/src/main/res/values-es/strings.xml")
    STRING_PATH2_ID = pathlib.Path(CURRENT_DIRECTORY).joinpath("../core/src/main/res/values-in/strings.xml")
    STRING_PATH2_PT = pathlib.Path(CURRENT_DIRECTORY).joinpath("../core/src/main/res/values-pt/strings.xml")

    
    paths = [
        STRING_PATH_CN,
        STRING_PATH2_CN,
        STRING_PATH_EN,
        STRING_PATH2_EN,
        STRING_PATH_JA,
        STRING_PATH2_JA,
        STRING_PATH_KO,
        STRING_PATH2_KO,
        STRING_PATH_DE,
        STRING_PATH2_DE,
        STRING_PATH_ES,
        STRING_PATH2_ES,
        STRING_PATH_ID,
        STRING_PATH2_ID,
        STRING_PATH_PT,
        STRING_PATH2_PT,
    ]

    for path in paths:
        if path.exists():
            path.unlink()  # 删除原文件

    for i, item in enumerate(all_sheet_in_excel):
        t = Spreadsheets(title=item.title, spreadsheet_token=obj_token,
                         saved_file_name=item.title + '.txt')
        t.simple_sheet_info = item
        sheet_list.append(t)

        try:
            get_content_write_to_file(t, paths[i], 0, True)  # 将数据写入对应文件路径的父目录下，文件名由t.saved_file_name指定
            count += 1
        except Exception as e:
            print(f"写入文件 {paths[i]} 时出错: {e}")
            raise (e)

    print(f"[1]共计{len(sheet_list)}在线配置文件,完成下载更新: {count}个")

if __name__ == "__main__":
    # 需要依次执行一下内容后获取到相关信息后，注释掉，以后就用不到了。建议保留。
    # get_wiki_spaces()
    #    name =三国人生,space_id=7329727468397395971
    # get_space_nodes("7366860334277197825")
    #    获取obj_token 填入到main函数中
    #   node_token=IhofwrMFlinWPUkHTs5csS4AnRe,obj_token=KrPrsQlbjh6VcKtoMhPcIryPn0I,obj_type=sheet,space_id=7329727468397395971,title=配置表

    #download_string()
    sheet_list.clear()
    main("Dv4WsYZfdhjyLxtGwXEcm8DUnhg", DIR_PATH, COMPRESS_FILE)
    


    
    pass
