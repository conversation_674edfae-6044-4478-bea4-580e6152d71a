package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.sub.bill.LeChenSdkManager.uploadRoleInfo
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PERMISSION
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.job.JobContent
import com.xingma.sdk.XmSdk
import com.xingma.sdk.bean.User
import com.xingma.sdk.callback.InitCallback
import com.xingma.sdk.callback.UserCallBack
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 穿山甲广告sdk初始化
 */
class TTRewardAdTask : JobContent<GameApp> {
    override fun execute(context: GameApp) {

    }
}