package com.moyu.chuanqirensheng.cloud.loginsdk

import androidx.activity.ComponentActivity
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.inittask.BuglyTask
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.sub.bill.LeChenSdkManager
import com.moyu.chuanqirensheng.sub.datastore.KEY_CUSTOM_AVATAR
import com.moyu.chuanqirensheng.sub.datastore.KEY_CUSTOM_USER_NAME
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PERMISSION
import com.moyu.chuanqirensheng.sub.datastore.KEY_OBJECT_ID
import com.moyu.chuanqirensheng.sub.datastore.KEY_VERIFIED
import com.moyu.chuanqirensheng.sub.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.GameSdkProcessor
import com.moyu.chuanqirensheng.sub.privacy.PrivacyManager
import com.moyu.chuanqirensheng.util.killSelf
import com.xingma.sdk.XmSdk
import com.xingma.sdk.bean.User
import com.xingma.sdk.callback.InitCallback
import com.xingma.sdk.callback.UserCallBack
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


class GameSdkDefaultProcessor : GameSdkProcessor {
    private val antiAddictionContent = mutableStateOf("")
    private val loginStatus = mutableStateOf(false)
    private val avatar = mutableStateOf<String?>(null)
    private val userName = mutableStateOf<String?>(null)
    private val antiAddictionStatus = mutableStateOf(false)
    private val objectId = mutableStateOf<String?>(null)

    private var isLogin = false
    private var isAntiAddict = false
    var lechenSdkInit = false


    override fun initGameSdk() {
        antiAddictionStatus.value = false
    }

    override fun initSDK(activity: ComponentActivity) {
        if (lechenSdkInit) return
        lechenSdkInit = true
        val userLoginCallBack: UserCallBack = object : UserCallBack {
            override fun onLoginFailed(msg: String) {
                "登录失败 $msg".toast()
            }

            override fun onLoginSuccess(user: User) {
                dealAfterLogin(user.user_id.take(6), user.user_id, "", activity)
            }

            override fun onLogout() {
            }
        }

        XmSdk.getInstance().init(GameApp.instance.activity, object : InitCallback {
            override fun onSuccess() {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    /* 登录监听方法只需要设置一次即可，切勿重复调用  */
                    XmSdk.getInstance().setLoginCallBack(userLoginCallBack)
                    setBooleanValueByKey(KEY_NEED_SHOW_PERMISSION, false)
                    BuglyTask().execute(GameApp.instance)
                    login(GameApp.instance.activity)
                }
            }

            override fun onError(msg: String) {
                "初始化失败：$msg".toast()
            }
        })
    }

    override fun login(activity: ComponentActivity) {
        try {
            if (isLogin) return
            isLogin = true

            XmSdk.getInstance().login(GameApp.instance.activity)
        } catch (e: Exception) {
            isLogin = false
            ("登录异常：" + e.message).toast()
        }
    }

    override fun antiAddictPassed(): MutableState<Boolean> {
        return mutableStateOf(true)
    }

    override fun hasLogin(): Boolean {
        return loginStatus.value
    }

    override fun getAvatarUrl(): String? {
        // 优先返回自定义头像，如果没有则返回默认头像
        val customAvatar = getStringFlowByKey(KEY_CUSTOM_AVATAR)
        return if (customAvatar.isNotEmpty()) {
            customAvatar
        } else {
            "skill_7019"
        }
    }

    override fun getUserName(): String? {
        // 优先返回自定义用户名，如果没有则返回默认用户名
        val customUserName = getStringFlowByKey(KEY_CUSTOM_USER_NAME)
        return if (customUserName.isNotEmpty()) {
            customUserName
        } else {
            userName.value
        }
    }

    override fun getObjectId(): String? {
        return objectId.value
    }

    override fun getAntiAddictionContent(): String {
        return antiAddictionContent.value
    }

    override fun dealAfterLogin(
        name: String,
        id: String,
        avatarUrl: String,
        activity: ComponentActivity
    ) {
        loginStatus.value = true
        userName.value = name// if (DebugManager.debug) "固定id" else name
        avatar.value = avatarUrl
        objectId.value = id // if (DebugManager.debug) "fixed_id_xxx" else id
//        checkAntiAddiction(activity)
        GameApp.globalScope.launch {
            val oldAccount = getStringFlowByKey(KEY_OBJECT_ID)
            if (oldAccount.isEmpty()) {
                setStringValueByKey(KEY_OBJECT_ID, objectId.value ?: "")
            } else if (oldAccount != objectId.value) {
                "不支持账号切换，请卸载重装".toast()
                delay(2000)
                killSelf()
            }
        }
        GameApp.instance.tryLogin()
    }

    override fun checkAntiAddiction(activity: ComponentActivity) {
        if (antiAddictionStatus.value || PrivacyManager.antiAddictVerified) {
            return
        }
        if (isAntiAddict) return
        isAntiAddict = true
        try {
            isAntiAddict = false
        } catch (e: Exception) {
            isAntiAddict = false
            ("防沉迷验证异常：" + e.message).toast()
            GameApp.globalScope.launch {
                setBooleanValueByKey(KEY_VERIFIED, true)
            }
            PrivacyManager.antiAddictVerified = true
        }
    }

    override fun isAgeIn8To16(): Boolean {
        return false
    }

    override fun isAgeUnder8(): Boolean {
        return false
    }

    fun isAdult(): Boolean {
        return true
    }

    override fun quitGame(onExit: () -> Unit) {

    }

    override fun uploadRoleInfo() {
        LeChenSdkManager.uploadRoleInfo()
    }
}