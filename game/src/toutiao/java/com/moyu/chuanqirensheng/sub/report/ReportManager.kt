package com.moyu.chuanqirensheng.sub.report

import com.moyu.chuanqirensheng.application.GameApp
import com.xingma.sdk.XmSdk
import com.xingma.sdk.callback.ExtraActionCallback
import com.xingma.sdk.impl.ExtraActionImpl

object ReportManager {

    fun init() {
    }

    fun onPurchaseCompletedAdjust(dollarPrice: Double, orderId: String) {}

    fun onLogin() {
    }

    fun pk(i: Int, value: Int) {

    }

    fun battle(i: Int, i1: Int, age: Int) {

    }

    fun onShopPurchase(sellId: Int, price: Int, priceType: Int) {

    }

    fun onNewGame(i: Int) {

    }

    fun onContinueGame(i: Int) {

    }

    fun onPage(route: String) {
    }

    fun onTalentUpgrade(level: Int) {
        report("${level}级")
    }

    fun onDungeonProgress(day: Int) {
        report("${day}天")
    }


        private fun report(action: String) {
        // 行为类型，不同的行为类型，功能不同；此处类型与回调中的type一致
        val type: Int = ExtraActionImpl.TYPE_ACTION_BEHAVIOR
        // 行为参数
        val map: MutableMap<String?, Any?> = HashMap<String?, Any?>()
        // action参数，对应所需记录打点的内容
        map.put("action", action)
        try {
            XmSdk.getInstance().extraAction(GameApp.instance.activity, type, map, object : ExtraActionCallback {
                override fun onSuccess(type: Int, infoJson: String?) {
                    // 上报成功
                }
                override fun onError(type: Int, msg: String?) {
                    // 上报失败
                }
            })
        } catch (throwable: Throwable) {
            // 此处报错可能因为改包不支持对应方法
            throwable.printStackTrace()
        }
    }
}