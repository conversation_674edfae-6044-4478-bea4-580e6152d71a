package com.moyu.chuanqirensheng.sub.bill

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.core.model.Sell
import com.xingma.sdk.XmSdk
import com.xingma.sdk.bean.PayInfo
import com.xingma.sdk.bean.RoleInfo
import com.xingma.sdk.callback.PayCallBack
import kotlinx.serialization.json.Json


object LeChenSdkManager {
    fun uploadRoleInfo() {
        GameApp.instance.getObjectId()?.let { objectId ->
            val roleInfo = RoleInfo()
            roleInfo.roleId = objectId
            roleInfo.roleName = GameApp.instance.getUserName() ?: "未登录"
            roleInfo.roleLevel = 1
            roleInfo.zoneId = (GameApp.instance.loginData.value.serverData.serverId + 1).toString()
            roleInfo.zoneName = "${GameApp.instance.loginData.value.serverData.serverId + 1}服"
            roleInfo.balance = 0
            roleInfo.vip = 0
            roleInfo.partyName = ""
            roleInfo.createTime = 0
            roleInfo.levelTime = 0
            XmSdk.getInstance().setRoleInfo("createRole", roleInfo)
        }
    }

    fun pay(gift: Sell, successCallback: () -> Unit, failedCallback: () -> Unit = {}) {
        if (!isNetTimeValid()) {
            return
        }
        GameApp.instance.getObjectId()?.let { objectId ->
            val payInfo = PayInfo()
            payInfo.amount = if (DebugManager.debug) 1 else gift.price * 100
            payInfo.productId = gift.id.toString()
            payInfo.productName = gift.name
            payInfo.count = 1
            payInfo.productDesc = gift.name
            val payData = PayData(
                userId = objectId,
                giftId = gift.id,
                giftUUID = gift.uuid,
                userName = (GameApp.instance.getUserName()?.take(10)) ?: "未登录"
            )
            payInfo.extraInfo = Json.encodeToString(PayData.serializer(), payData)
            val roleInfo = RoleInfo()
            roleInfo.roleId = objectId
            roleInfo.roleName = GameApp.instance.getUserName() ?: "未登录"
            roleInfo.roleLevel = 1
            roleInfo.zoneId = (GameApp.instance.loginData.value.serverData.serverId + 1).toString()
            roleInfo.zoneName = "${GameApp.instance.loginData.value.serverData.serverId + 1}服"
            roleInfo.balance = 0
            roleInfo.vip = 0
            roleInfo.partyName = ""
            roleInfo.createTime = 0
            roleInfo.levelTime = 0
            payInfo.roleInfo = roleInfo
            XmSdk.getInstance().pay(GameApp.instance.activity, payInfo, object : PayCallBack {
                override fun onSuccess() {
                    /* 接到此回调，证明支付操作完成，不代表支付成功，具体是否成功需要以服务器通知为准 */
                    successCallback()
                }

                override fun onError(msg: String) {
                    /* 支付失败，msg:失败原因（包括取消支付的情况）*/
                    msg.toast()
                    failedCallback()
                }
            })
        } ?: "登录信息有误，无法购买".toast()
    }
}