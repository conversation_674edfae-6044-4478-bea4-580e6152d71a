package com.moyu.chuanqirensheng.sub.bill


import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.sub.datastore.KEY_PAY_DATA
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.core.model.Sell
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.UUID

const val TEST_ID = "a000001"

object BillingManager {
    val payClientDataList = mutableStateListOf<PayClientData>()

    val isPaying = mutableStateOf(false)

    init {
        getListObject<PayClientData>(KEY_PAY_DATA).takeIf { it.isNotEmpty() }?.let {
            payClientDataList.addAll(it)
        }
    }

    //initiate purchase on consume button click
    fun consume(productId: String) {

    }

    fun queryAsync() {

    }

    suspend fun prepay(sell: Sell, award: () -> Unit) {
        val realSell = sell.copy(uuid = UUID.randomUUID().toString().take(12))
        if (DebugManager.freeShop) {
            award()
            return
        }
        if (isPaying.value) {
            GameApp.instance.getWrapString(R.string.is_paying).toast()
            return
        }
        if (GameApp.instance.isAgeUnder8()) {
            GameApp.instance.getWrapString(R.string.child_pay_tips).toast()
            return
        }

        isPaying.value = true
        GameApp.globalScope.launch(Dispatchers.IO) {
            async {
                // 防止玩家快速点击，每两秒最多下单1次
                delay(2000)
                isPaying.value = false
            }
        }


        val payClientData = PayClientData(
            userId = GameApp.instance.getObjectId() ?: "",
            orderName = realSell.name,
            totalMoneyInCent = realSell.price * 100,
            tradeNo = realSell.uuid,
            award = award
        )

        LeChenSdkManager.pay(realSell, successCallback = {
            Dialogs.payBlockingDialog.value =
                GameApp.instance.getWrapString(R.string.pay_blocking_tips)
            GameApp.globalScope.launch(Dispatchers.Main) {
                RetrofitModel.getBill(
                    userId = GameApp.instance.getObjectId()!!,
                    giftId = realSell.id.toString(),
                    uuid = realSell.uuid
                ).let {
                    if (it.succeeded) {
                        removePayClientData(payClientData)
                        Dialogs.payBlockingDialog.value = null
                        checkIfCanAward(payClientData)
                    } else {
                        if (Dialogs.payBlockingDialog.value != null) {
                            it.message.toast()
                            Dialogs.alertDialog.value = CommonAlert(
                                title = GameApp.instance.getWrapString(R.string.error_order),
                                content =
                                    GameApp.instance.getWrapString(
                                        R.string.error_order_info,
                                        payClientData.orderName,
                                        payClientData.tradeNo + payClientData.userId,
                                        GameApp.instance.getUserName() ?: "",
                                        "支付失败"
                                    )
                            )
                            Dialogs.payBlockingDialog.value = null
                        }
                    }
                    Dialogs.payBlockingDialog.value = null
                }
            }
        }) {
            Dialogs.alertDialog.value = CommonAlert(
                title = GameApp.instance.getWrapString(R.string.error_order),
                content =
                    GameApp.instance.getWrapString(
                        R.string.error_order_info,
                        payClientData.orderName,
                        payClientData.tradeNo + payClientData.userId,
                        GameApp.instance.getUserName() ?: "",
                        "支付失败"
                    )
            )
            Dialogs.payBlockingDialog.value = null
        }
    }

    private fun checkIfCanAward(payClientData: PayClientData) {
        if (getBooleanFlowByKey(payClientData.tradeNo.reversed().replace("-", ""))) {
            return
        } else {
            setBooleanValueByKey(payClientData.tradeNo.reversed().replace("-", ""), true)
            payClientData.award()
//            val reportOrder = MiReportOrder()
//            reportOrder.cpOrderId = payClientData.tradeNo
//            reportOrder.isDelivery = true
//            reportOrder.errMsg = "发货成功"
//            MiCommplatform.getInstance().miReportOrder(reportOrder)
        }
    }

    fun removePayClientData(payData: PayClientData) {
        payClientDataList.remove(payData)
        setListObject(KEY_PAY_DATA, payClientDataList)
    }
}