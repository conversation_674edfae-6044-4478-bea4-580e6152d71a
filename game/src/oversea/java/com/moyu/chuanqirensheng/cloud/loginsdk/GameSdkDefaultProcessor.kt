package com.moyu.chuanqirensheng.cloud.loginsdk

import androidx.activity.ComponentActivity
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.sub.bill.RC_SIGN_IN
import com.moyu.chuanqirensheng.sub.datastore.KEY_OBJECT_ID
import com.moyu.chuanqirensheng.sub.datastore.KEY_CUSTOM_USER_NAME
import com.moyu.chuanqirensheng.sub.datastore.KEY_CUSTOM_AVATAR
import com.moyu.chuanqirensheng.sub.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.GameSdkProcessor
import com.moyu.chuanqirensheng.util.killSelf
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking


class GameSdkDefaultProcessor : GameSdkProcessor {
    private val antiAddictionContent = mutableStateOf("")
    private val loginStatus = mutableStateOf(false)
    private val avatar = mutableStateOf<String?>(null)
    private val userName = mutableStateOf<String?>(null)
    private val antiAddictionStatus = mutableStateOf(false)
    private val objectId = mutableStateOf<String?>(null)

    private var init = false
    private var mGoogleSignInClient: GoogleSignInClient? = null
    var mGoogleSignInAccount: GoogleSignInAccount? = null

    override fun initGameSdk() {
        antiAddictionStatus.value = false
    }

    // 初始化SDK，判断是否有登录态，没有则调用好游快爆登录，
    // 登录后校验防沉迷，若返回登录成功，则正常显示菜单，否则弹窗提示错误原因，并退出
    override fun initSDK(activity: ComponentActivity) {
        if (init) return
        init = true

        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestEmail()
            .build()
        mGoogleSignInClient = GoogleSignIn.getClient(activity, gso)
        login(activity)
    }

    override fun login(activity: ComponentActivity) {
        GoogleSignIn.getLastSignedInAccount(activity)?.let {account ->
            mGoogleSignInAccount = account
            dealAfterLogin(account.displayName?: "No Name", account.id!!, account.photoUrl.toString(), activity)
        } ?: kotlin.run {
            val signInIntent = mGoogleSignInClient!!.signInIntent
            activity.startActivityForResult(signInIntent, RC_SIGN_IN)
        }
    }

    override fun antiAddictPassed(): MutableState<Boolean> {
        return antiAddictionStatus
    }

    override fun hasLogin(): Boolean {
        return loginStatus.value
    }

    override fun getAvatarUrl(): String {
        // 优先返回自定义头像，如果没有则返回默认头像
        val customAvatar = getStringFlowByKey(KEY_CUSTOM_AVATAR)
        return if (customAvatar.isNotEmpty()) {
            customAvatar
        } else {
            avatar.value ?: "skill_7019"
        }
    }

    override fun getUserName(): String? {
        // 优先返回自定义用户名，如果没有则返回默认用户名
        val customUserName = getStringFlowByKey(KEY_CUSTOM_USER_NAME)
        return if (customUserName.isNotEmpty()) {
            customUserName
        } else {
            userName.value
        }
    }

    override fun getObjectId(): String? {
        return objectId.value
    }

    override fun getAntiAddictionContent(): String {
        return antiAddictionContent.value
    }

    override fun dealAfterLogin(name: String, id: String, avatarUrl: String, activity: ComponentActivity) {
        loginStatus.value = true
        userName.value = name
        objectId.value = id
        avatar.value = avatarUrl
        checkAntiAddiction(activity)
        GameApp.globalScope.launch {
            val oldAccount = getStringFlowByKey(KEY_OBJECT_ID)
            if (oldAccount.isEmpty()) {
                setStringValueByKey(KEY_OBJECT_ID, objectId.value ?: "")
            } else if (oldAccount != objectId.value) {
                "不支持账号切换，请卸载重装".toast()
                delay(2000)
                killSelf()
            }
        }
        GameApp.instance.tryLogin()
    }

    override fun isAgeUnder8(): Boolean {
        return false
    }

    override fun isAgeIn8To16(): Boolean {
        return false
    }

    override fun checkAntiAddiction(activity: ComponentActivity) {
    }

    override fun quitGame(onExit: () -> Unit) {
        onExit()
    }

    override fun uploadRoleInfo() {

    }
}
