package com.moyu.chuanqirensheng.application.inittask

import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustConfig
import com.adjust.sdk.LogLevel
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.job.JobContent

/**
 * Adjsut sdk 初始化
 */
class AdjustTask : JobContent<GameApp> {
    override suspend fun execute(context: GameApp) {
        val appToken = "qxy35ry0zqps"

        val environment = if (DebugManager.isLite) {
            AdjustConfig.ENVIRONMENT_SANDBOX
        } else {
            AdjustConfig.ENVIRONMENT_PRODUCTION
        }

        val config = AdjustConfig(context, appToken, environment)
        if (DebugManager.isLite) {
            config.setLogLevel(LogLevel.VERBOSE)
        }

        Adjust.initSdk(config)
    }
}