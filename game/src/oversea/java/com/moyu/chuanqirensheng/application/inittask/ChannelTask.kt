package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.sub.job.JobContent

/**
 * 穿山甲广告sdk初始化
 */
class ChannelTask : JobContent<GameApp> {
    override fun execute(context: GameApp) {
        if (!Dialogs.showPrivacyDialog.value && !Dialogs.showPermissionDialog.value) {

        }
    }
}