package com.xingma.sdk

import com.moyu.chuanqirensheng.application.GameActivity

val obj = XmSdk()

class XmSdk {
    fun onCreate(activity: GameActivity) {

    }

    fun onStart(activity: GameActivity) {}
    fun onRestart(activity: com.moyu.chuanqirensheng.application.GameActivity) {}
    fun onPause(activity: com.moyu.chuanqirensheng.application.GameActivity) {}
    fun onStop(activity: GameActivity) {}
    fun onDestroy(activity: GameActivity) {}
    fun onNewIntent(intent: android.content.Intent?) {}
    fun onSaveInstanceState(bundle: android.os.Bundle) {}
    fun onConfigurationChanged(configuration: android.content.res.Configuration) {}
    fun onRequestPermissionsResult(i: Int,  permissions: Array<out String>, ints: IntArray) {}
    fun onResume(activity: com.moyu.chuanqirensheng.application.GameActivity) {}
    fun onActivityResult(i: Int, i2: Int, intent: android.content.Intent?) {}

    companion object {
        fun getInstance(): XmSdk {
            return obj
        }
    }

}