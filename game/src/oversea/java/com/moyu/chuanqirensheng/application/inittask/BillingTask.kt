package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.sub.job.JobContent
import kotlinx.coroutines.launch

/**
 * 谷歌订单sdk初始化
 */
class BillingTask : JobContent<GameApp> {
    override fun execute(context: GameApp) {
        GameApp.globalScope.launch {
            BillingManager.initBillingClient()
        }
    }
}
