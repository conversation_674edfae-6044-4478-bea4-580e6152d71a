package com.moyu.chuanqirensheng.application.inittask

//import com.facebook.FacebookSdk
import com.google.android.gms.ads.MobileAds
import com.google.firebase.analytics.FirebaseAnalytics
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.sub.ad.AdHolder
import com.moyu.chuanqirensheng.sub.job.JobContent
import com.moyu.chuanqirensheng.sub.report.ReportManager.firebaseAnalytics

//import com.facebook.appevents.AppEventsLogger;


/**
 * 穿山甲广告sdk初始化
 */
class TTRewardAdTask : JobContent<GameApp> {
    override fun execute(context: GameApp) {
        MobileAds.initialize(context) {
            AdHolder.adInit = true
        }

//        AppsFlyerLib.getInstance().init(appFlyerDevKey, null, context)
//        AppsFlyerLib.getInstance().start(context)

        // 初始化 Firebase Analytics
        firebaseAnalytics = FirebaseAnalytics.getInstance(context)

        // facebook
        // 设置是否允许收集广告 ID
//        FacebookSdk.setAdvertiserIDCollectionEnabled(true); // 或 false，看你的需求
        // 初始化 Facebook SDK（确保在 setAdvertiserIDCollectionEnabled 之后）
//        FacebookSdk.sdkInitialize(context);
//        AppEventsLogger.activateApp(context);
    }
}