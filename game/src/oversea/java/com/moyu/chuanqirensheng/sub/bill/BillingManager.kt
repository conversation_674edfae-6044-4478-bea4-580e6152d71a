package com.moyu.chuanqirensheng.sub.bill

import android.util.Log
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClient.SkuType.INAPP
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ConsumeParams
import com.android.billingclient.api.ConsumeResponseListener
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.sell.SELL_FOREVER
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_GIFT_AWARDED
import com.moyu.chuanqirensheng.sub.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.core.GameCore
import com.moyu.core.model.Award
import com.moyu.core.model.Sell
import com.moyu.core.model.toAward
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber


object BillingManager : PurchasesUpdatedListener {

    // 对齐国内订单，不需要内容
    val payClientDataList = emptyList<PayClientData>()

    lateinit var billingClient: BillingClient

    private var consumeListener =
        ConsumeResponseListener { billingResult, purchaseToken ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                "Item Consumed".toast()
            }
        }

    /**
     * 初始化billingClient
     */
    suspend fun initBillingClient() {
        withContext(Dispatchers.IO) {
            billingClient = BillingClient.newBuilder(GameApp.instance)
                .setListener(this@BillingManager)
                .enablePendingPurchases()
                .build()
            startConnect()
        }
    }

    fun queryAsync() {
        GameApp.globalScope.launch(Dispatchers.IO) {
            if (billingClient.isReady) {
                val params = com.android.billingclient.api.QueryPurchasesParams.newBuilder()
                    .setProductType(INAPP)
                    .build()
                billingClient.queryPurchasesAsync(params) { billingResult, purchases ->
                    onPurchasesUpdated(billingResult, purchases)
                }
            }
        }
    }

    fun handlePurchases(purchases: List<Purchase>) {
        for (purchase in purchases) {
            //if item is purchased
            val extraParam = purchase.accountIdentifiers?.obfuscatedAccountId
            val shopList = repo.gameCore.getSellPool().filter { it.isAifadian() }
            if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
                val sell = if (!extraParam.isNullOrEmpty()) {
                    shopList.find { it.id.toString() == extraParam }
                } else {
                    shopList.find { it.googleItemId in purchase.products }
                }
                sell?.let {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        if (getStringFlowByKey(purchase.purchaseToken).isEmpty()) {
                            setStringValueByKey(purchase.purchaseToken, "true")
                            var realAward = Award()
                            repeat(purchase.quantity) { _ ->
                                realAward += it.toAward()
                            }

                            // 上报给第三方平台
                            ReportManager.onPurchaseCompletedAdjust(purchase.quantity * it.priceDollar, purchase.orderId ?: purchase.purchaseToken)

                            if (it.isGift()) {
                                setBooleanValueByKey(KEY_GIFT_AWARDED + it.id, true)
                                setBooleanValueByKey(SELL_FOREVER + it.id, true)
                                SellManager.openSellChest(it)
                                if (repo.inGame.value && !repo.gameMode.value.isAnyPvpMode()) {
                                    // todo 礼包奖励，如果在局内，直接局内也要获得盟友卡
                                    AwardManager.gainAward(Award(allies = it.toAward().outAllies))
                                }
                                GameCore.instance.onBattleEffect(SoundEffect.BuyGood)
                            } else if (it.isTower()) {
                                TowerManager.openPackage(it)
                            } else if (sell.isMonthCard()) {
                                MonthCardManager.openPackage(sell)
                            } else {
                                SellManager.openGoogleBillSell(it)
                                ReportManager.onPurchaseCompletedAF(
                                    it.id.toString(),
                                    realAward.electric.toDouble(),
                                    purchase.quantity
                                )
                                AwardManager.gainAward(realAward)
                                Dialogs.awardDialog.value = realAward
                                if (it.isNewTaskPackage()) {
                                    SevenDayManager.markGoogleSellItem(it)
                                }
                            }
                        }
                    }
                }
                //if item is purchased and not consumed
                if (!purchase.isAcknowledged) {
                    val consumeParams = ConsumeParams.newBuilder()
                        .setPurchaseToken(purchase.purchaseToken)
                        .build()
                    billingClient.consumeAsync(consumeParams, consumeListener)
                }
            } else if (purchase.purchaseState == Purchase.PurchaseState.PENDING) {
                "Purchase is Pending. Please complete Transaction".toast()
            } else if (purchase.purchaseState == Purchase.PurchaseState.UNSPECIFIED_STATE) {
                "Purchase Status Unknown".toast()
            }
        }
    }


    //initiate purchase on consume button click
    fun consume(productId: String, sellId: String = "") {
        //check if service is already connected
        GameApp.globalScope.launch(Dispatchers.IO) {
            if (billingClient.isReady) {
                initiatePurchase(productId, sellId)
            } else {
                startConnect(productId, sellId)
            }
        }
    }

    private suspend fun startConnect(productId: String? = null, extraParam: String = "") {
        withContext(Dispatchers.IO) {
            billingClient.startConnection(object : BillingClientStateListener {
                override fun onBillingSetupFinished(billingResult: BillingResult) {
                    if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                        if (productId == null) {
                            queryAsync()
                        } else {
                            initiatePurchase(productId, extraParam)
                        }
                    } else {
                        ("Error " + billingResult.debugMessage).toast()
                    }
                }

                override fun onBillingServiceDisconnected() {}
            })
        }
    }

    private fun initiatePurchase(productId: String, extraParam: String) {
        val productList = listOf(
            QueryProductDetailsParams.Product.newBuilder()
                .setProductId(productId)
                .setProductType(INAPP)
                .build()
        )
        val params = QueryProductDetailsParams.newBuilder()
            .setProductList(productList)
            .build()

        billingClient.queryProductDetailsAsync(params) { billingResult, productDetailsList ->

        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
            if (productDetailsList.isNotEmpty()) {
                val productDetailsParamsList = listOf(
                    BillingFlowParams.ProductDetailsParams.newBuilder()
                        .setProductDetails(productDetailsList[0])
                        .build()
                )
                val flowParams = BillingFlowParams.newBuilder()
                    .setProductDetailsParamsList(productDetailsParamsList)
                    .setObfuscatedAccountId(extraParam)
                        .build()
                    billingClient.launchBillingFlow(GameApp.instance.activity, flowParams)
                } else {
                    //try to add item/product id "consumable" inside managed product in google play console
                    "Purchase Item not Found".toast()
                }
            } else {
                (" Error " + billingResult.debugMessage).toast()
            }
        }
    }

    override fun onPurchasesUpdated(
        billingResult: BillingResult,
        purchases: MutableList<Purchase>?
    ) {
        //交易更新将会在这里回调
        val responseCode: Int = billingResult.responseCode
        if (responseCode == BillingClient.BillingResponseCode.OK && purchases != null) {
            handlePurchases(purchases)
        } else if (responseCode == BillingClient.BillingResponseCode.USER_CANCELED) {
            //取消支付
            "Purchase Canceled".toast()
        } else if (responseCode == BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED) {
            //已存在这个未完成订单，查询订单验证然后消耗掉
            "Purchase Owned".toast()
            billingClient.queryPurchasesAsync(INAPP) { billingResult, purchases ->
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK && purchases != null) {
                    Timber.e("test:ITEM_ALREADY_OWNED")
                    handlePurchases(purchases)
                }
            }
        } else {
            //还有很多其他状态，判断进行相应处理
            ("Error " + billingResult.debugMessage).toast()
        }
    }

    fun prepay(sell: Sell, function: () -> Unit) {
        consume(sell.googleItemId, sell.id.toString())
    }

    fun removePayClientData(it: PayClientData) {

    }
}
