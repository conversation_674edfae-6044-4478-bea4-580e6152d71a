package com.moyu.chuanqirensheng.sub.review

import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.core.content.ContextCompat.startActivity
import com.google.android.play.core.review.ReviewInfo
import com.google.android.play.core.review.ReviewManager
import com.google.android.play.core.review.ReviewManagerFactory
import com.google.android.gms.tasks.Task
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast


fun requestInAppReview() {
    val manager: ReviewManager = ReviewManagerFactory.create(GameApp.instance)
    val request: Task<ReviewInfo> =
        manager.requestReviewFlow()
    request.addOnCompleteListener { task ->
        if (task.isSuccessful) {
            // 获取ReviewInfo对象
            val reviewInfo: ReviewInfo = task.result
            val flow: Task<Void> =
                manager.launchReviewFlow(GameApp.instance.activity, reviewInfo)
            flow.addOnCompleteListener { _ ->
                GameApp.instance.getWrapString(R.string.review_result_toast).toast()
            }
        } else {
            // 处理错误
            // 例如，你可以引导用户到Google Play商店页面进行评分
            openAppInGooglePlay(GameApp.instance.packageName)
        }
    }
}

fun openAppInGooglePlay(packageName: String) {
    try {
        // 尝试打开Google Play应用
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName"))
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(GameApp.instance.activity, intent, Bundle())
    } catch (e: ActivityNotFoundException) {
        // 如果Google Play应用不可用，则打开网页版本
        val intent = Intent(
            Intent.ACTION_VIEW,
            Uri.parse("https://play.google.com/store/apps/details?id=$packageName")
        )
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(GameApp.instance.activity, intent, Bundle())
    }
}