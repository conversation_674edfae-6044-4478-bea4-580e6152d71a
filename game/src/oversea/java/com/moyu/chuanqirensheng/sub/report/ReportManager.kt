package com.moyu.chuanqirensheng.sub.report

import android.os.Bundle
import androidx.compose.runtime.mutableStateOf
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustEvent
import com.google.firebase.analytics.FirebaseAnalytics
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPORT_AF_AD
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPORT_AF_PURCHASE1
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPORT_AF_PURCHASE2
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPORT_AF_SECOND_LOGIN
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.util.getVersionCode

const val AF_AD_ID = "af_ad_id"
const val AF_NEW_USER = "af_new_user"
const val AF_SECOND_PURCHASE = "af_second_purchase"

const val KEY_PURCHASE = "purchase_in_shop"
const val KEY_START_GAME = "start_game"
const val KEY_CONTINUE_GAME = "continue_game"
const val KEY_PK_GAME = "pk_game"
const val KEY_BATTLE_GAME = "battle_in_game"
const val KEY_REVIEW_GAME = "review_game"
const val KEY_PRELOAD_AD = "preload_ad"
const val KEY_LOAD_AD = "load_ad"
const val KEY_PAGE = "page"

const val PARAM_GAME_MODE = "param_game_mode"
const val PARAM_PURCHASE_TYPE = "param_purchase_type"
const val PARAM_BATTLE_RESULT = "param_battle_result"
const val PARAM_PVP_SCORE = "param_pvp_score"
const val PARAM_STAGE = "param_stage"
const val PARAM_STAR = "param_star"
const val PARAM_KEY = "param_key"
const val PARAM_DIAMOND = "param_diamond"
const val PARAM_ELECTRIC = "param_electric"
const val PARAM_PRELOAD_TYPE = "param_preload_type" // 0 进入Ad页面 1 观看广告


object ReportManager {
    // 初始化 Firebase Analytics
    lateinit var firebaseAnalytics: FirebaseAnalytics
    val firstAFAd = mutableStateOf(false)
    val firstAFPurchase = mutableStateOf(false)
    val secondAFPurchase = mutableStateOf(false)
    val secondAFLogin = mutableStateOf(false)

    fun init() {
        firstAFAd.value = getBooleanFlowByKey(KEY_REPORT_AF_AD)
        firstAFPurchase.value = getBooleanFlowByKey(KEY_REPORT_AF_PURCHASE1)
        secondAFPurchase.value = getBooleanFlowByKey(KEY_REPORT_AF_PURCHASE2)
        secondAFLogin.value = getBooleanFlowByKey(KEY_REPORT_AF_SECOND_LOGIN)
    }

    fun onLogin() {
        val eventValues: MutableMap<String, Any> = HashMap()
        eventValues[AF_NEW_USER] = if (GameApp.newUser) 1 else 0
        eventValues["af_new_version"] = getVersionCode()
        eventValues["af_customer_user_id"] = GameApp.instance.getObjectId()?:"none"
        if (!GameApp.newUser && !secondAFLogin.value) {
            secondAFLogin.value = true
            setBooleanValueByKey(KEY_REPORT_AF_SECOND_LOGIN, true)
//            AppsFlyerLib.getInstance().logEvent(GameApp.instance, "af_login", eventValues)
        } else {
            if (GameApp.newUser) {
//                AppsFlyerLib.getInstance()
//                    .logEvent(GameApp.instance, "af_login", eventValues)
            }
        }


        // 定义一个购买事件
        val bundle = bundleWithCommonParam()
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.LOGIN, bundle)
    }

    fun onAdCompletedAF(adId: String) {
        if (!firstAFAd.value) {
            firstAFAd.value = true
            setBooleanValueByKey(KEY_REPORT_AF_AD, true)
            val eventValues: MutableMap<String, Any> = HashMap()
            eventValues[AF_AD_ID] = adId
            eventValues[AF_NEW_USER] = if (GameApp.newUser) 1 else 0
            eventValues["af_new_version"] = getVersionCode()
            eventValues["af_customer_user_id"] = GameApp.instance.getObjectId()?:"none"
//            AppsFlyerLib.getInstance().logEvent(GameApp.instance, "af_ad_view", eventValues)
        }

        // 定义一个购买事件
        val bundle = bundleWithCommonParam()
        bundle.putString(AF_AD_ID, adId)
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.AD_IMPRESSION, bundle)
    }

    fun onPurchaseCompletedAF(purchaseId: String, amount: Double, number: Int) {
        val eventValues: MutableMap<String, Any> = HashMap()
        eventValues["af_revenue"] = amount
        eventValues["af_content_id"] = purchaseId
        eventValues[AF_NEW_USER] = if (GameApp.newUser) 1 else 0
        eventValues["af_new_version"] = getVersionCode()
        eventValues["af_quantity"] = number
        eventValues["af_customer_user_id"] = GameApp.instance.getObjectId()?:"none"
        if (!firstAFPurchase.value) {
            firstAFPurchase.value = true
            setBooleanValueByKey(KEY_REPORT_AF_PURCHASE1, true)
            eventValues[AF_SECOND_PURCHASE] = 0
//            AppsFlyerLib.getInstance().logEvent(GameApp.instance, "af_purchase", eventValues)
        } else if (!secondAFPurchase.value) {
            secondAFPurchase.value = true
            setBooleanValueByKey(KEY_REPORT_AF_PURCHASE2, true)
            eventValues[AF_SECOND_PURCHASE] = 1
//            AppsFlyerLib.getInstance().logEvent(GameApp.instance, "af_purchase", eventValues)
        }

        val bundle = bundleWithCommonParam()
        bundle.putInt("af_revenue", amount.toInt())
        bundle.putString("af_content_id", purchaseId)
        bundle.putInt("af_quantity", number)
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.REFUND, bundle)
    }

    // Adjust 上报埋点
    fun onPurchaseCompletedAdjust(dollarPrice: Double, orderId: String) {
        // 创建 Adjust 事件
        val adjustEvent = AdjustEvent("e40xeo")
        adjustEvent.setRevenue(dollarPrice, "USD") // 美元

        if (orderId.isNotEmpty()) {
            // 推荐设置 transactionId，防止重复上报
            adjustEvent.deduplicationId = orderId
        }

        Adjust.trackEvent(adjustEvent)
    }

    fun onShopPurchase(sellId: Int, price: Int, priceType: Int) {
        val bundle = Bundle()
        bundle.putInt("af_revenue", price)
        bundle.putInt(PARAM_PURCHASE_TYPE, priceType)
        bundle.putString("af_content_id", sellId.toString())
        firebaseAnalytics.logEvent(KEY_PURCHASE, bundle)
    }

    fun onNewGame(mode: Int) {
        val bundle = bundleWithCommonParam()
        bundle.putInt(AF_NEW_USER, if (GameApp.newUser) 1 else 0)
        bundle.putInt(PARAM_GAME_MODE, mode)
        bundle.putInt("af_new_version", getVersionCode())
        bundle.putString("af_customer_user_id", GameApp.instance.getObjectId()?:"none")
        firebaseAnalytics.logEvent(KEY_START_GAME, bundle)
    }

    fun onContinueGame(mode: Int) {
        val bundle = bundleWithCommonParam()
        bundle.putInt(PARAM_GAME_MODE, mode)
        firebaseAnalytics.logEvent(KEY_CONTINUE_GAME, bundle)
    }

    fun pk(win: Int, score: Int) {
        val bundle = bundleWithCommonParam()
        bundle.putInt(PARAM_BATTLE_RESULT, win)
        bundle.putInt(PARAM_PVP_SCORE, score)
        firebaseAnalytics.logEvent(KEY_PK_GAME, bundle)
    }

    fun battle(win: Int, mode: Int, stage: Int) {
        val bundle = bundleWithCommonParam()
        bundle.putInt(PARAM_BATTLE_RESULT, win)
        bundle.putInt(PARAM_GAME_MODE, mode)
        bundle.putInt(PARAM_STAGE, stage)
        firebaseAnalytics.logEvent(KEY_BATTLE_GAME, bundle)
    }

    private fun bundleWithCommonParam():Bundle {
        val bundle = Bundle()
        bundle.putInt(PARAM_KEY, AwardManager.key.value)
        bundle.putInt(PARAM_DIAMOND, AwardManager.diamond.value)
        bundle.putInt(PARAM_ELECTRIC, AwardManager.electric.value)
        bundle.putInt(AF_NEW_USER, if (GameApp.newUser) 1 else 0)
        bundle.putInt("af_new_version", getVersionCode())
        bundle.putString("af_customer_user_id", GameApp.instance.getObjectId()?:"none")
        return bundle
    }

    fun onLoadAd() {
        val bundle = bundleWithCommonParam()
        firebaseAnalytics.logEvent(KEY_LOAD_AD, bundle)
    }

    fun onPage(route: String) {
        val bundle = bundleWithCommonParam()
        bundle.putString(KEY_PAGE, route)
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)
    }

    fun onTalentUpgrade(level: Int) {
    }

    fun onDungeonProgress(day: Int) {
    }
}