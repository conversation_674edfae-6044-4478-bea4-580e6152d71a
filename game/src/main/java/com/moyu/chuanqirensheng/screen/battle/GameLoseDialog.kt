package com.moyu.chuanqirensheng.screen.battle

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.router.PVP2_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.core.model.role.Role

@Composable
fun GameLoseDialog(result: MutableState<List<Role>>) {
    result.value.takeIf { it.isNotEmpty() }?.let { roles ->
        LaunchedEffect(Unit) {
            // 关闭弹窗，防止作弊：
            // 战斗的时候，点击投降 出现投降界面，然后不动，等胜利后，发现可以正常领取胜利的奖励后还会出现投降界面，投降后还会继续获得投降的奖励，但是挑战次数被占用，导致次数显示未用完但是无法继续挑战
            Dialogs.alertDialog.value = null
        }
        DisposableEffect(Unit) {
            onDispose {
                if (repo.gameMode.value.isPvp1Mode()) {
                    PvpManager.currentTarget.value = RankData(System.currentTimeMillis())
                    goto(PVP_CHOOSE_ENEMY_SCREEN)
                } else if (repo.gameMode.value.isPvp2Mode()) {
                    Pvp2Manager.currentTarget.value = RankData(System.currentTimeMillis())
                    goto(PVP2_CHOOSE_ENEMY_SCREEN)
                } else if (repo.gameMode.value.isTowerMode()) {
                    goto(TOWER_SCREEN)
                } else {
                    EventManager.doEventBattleResult(
                        EventManager.selectedEvent.value, false,
                        forceQuit = false
                    )
                }
            }
        }
        PanelDialog(onDismissRequest = {
            result.value = emptyList()
        }, contentBelow = {
            GameButton(
                text = stringResource(R.string.statistic),
                buttonStyle = ButtonStyle.Blue
            ) {
                Dialogs.statisticView.value = true
            }
            GameButton(text = stringResource(id = R.string.battle_report), buttonStyle = ButtonStyle.Orange) {
                Dialogs.infoDialog.value = true
            }
        }) {
            Column(
                Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding10)
            ) {
                Spacer(modifier = Modifier.size(padding10))
                if (repo.gameMode.value.isPvp1Mode()) {
                    Text(
                        text = stringResource(R.string.failed_to, PvpManager.currentTarget.value.userName),
                        style = MaterialTheme.typography.h2,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.size(padding4))
                } else if (repo.gameMode.value.isPvp2Mode()) {
                    Text(
                        text = stringResource(R.string.failed_to, Pvp2Manager.currentTarget.value.userName),
                        style = MaterialTheme.typography.h2,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.size(padding4))
                } else {
                    roles.first { !it.isPlayerSide() }.let {
                        Text(
                            text = stringResource(R.string.failed_to, it.getRace().name),
                            style = MaterialTheme.typography.h2,
                            color = Color.Black
                        )
                        Spacer(modifier = Modifier.size(padding4))
                    }
                }
                Spacer(modifier = Modifier.size(padding10))
                roles.filter { it.isPlayerSide() }.filter { it.isDeath() }.forEach {
                    Text(
                        text = stringResource(
                            R.string.die_in_battle, it.getRace().name
                        ), style = MaterialTheme.typography.h2, color = Color.Black
                    )
                    Spacer(modifier = Modifier.size(padding4))
                }
            }
        }
    }
}