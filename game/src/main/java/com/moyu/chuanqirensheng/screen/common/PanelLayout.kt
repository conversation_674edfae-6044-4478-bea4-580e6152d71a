package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding165
import com.moyu.chuanqirensheng.ui.theme.padding220
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding380
import com.moyu.chuanqirensheng.ui.theme.padding420
import com.moyu.chuanqirensheng.ui.theme.padding8


enum class PanelSize(val width: Dp, val height: Dp, val background: Int) {
    Small(padding300, padding165, R.drawable.common_window3),
    SmallPlus(padding380, padding220, R.drawable.common_window3),
    Normal(padding380, padding420, R.drawable.common_window2),
}

@Composable
fun PanelLayout(
    modifier: Modifier = Modifier,
    size: PanelSize,
    showClose: Boolean = false,
    onClose: () -> Unit = {},
    content: @Composable (BoxScope.() -> Unit),
) {
    EffectButton(
        modifier = modifier.size(size.width, size.height)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = size.background),
            contentDescription = null
        )
        if (showClose) {
            CloseButton(
                Modifier
                    .align(Alignment.TopEnd)
                    .graphicsLayer {
                        translationX = -padding8.toPx()
                        translationY = padding8.toPx()
                    }) {
                onClose()
            }
        }
        Box(
            modifier = Modifier.padding(
                horizontal = size.width / 12,
                vertical = size.height / 12
            )
        ) {
            content()
        }
    }
}

@Composable
fun CloseButton(modifier: Modifier, onClick: () -> Unit) {
    EffectButton(modifier = modifier
        .semantics {
            contentDescription = GameApp.instance.getString(R.string.close)
        }
        .size(imageMedium),
        onClick = {
            onClick()
        }) {
        Image(
            modifier = Modifier
                .fillMaxSize(),
            contentScale = ContentScale.FillHeight,
            painter = painterResource(id = R.drawable.common_exit),
            contentDescription = stringResource(R.string.quit_page)
        )
    }
}
