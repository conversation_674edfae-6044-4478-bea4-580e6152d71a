package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.moneyHeight
import com.moyu.chuanqirensheng.ui.theme.moneyWidth
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding180
import com.moyu.chuanqirensheng.ui.theme.padding220
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding66

enum class LabelSize(val width: Dp, val height: Dp) {
    Small(imageSmallPlus, imageSmall),
    Medium(padding180, padding36),
    Medium2(moneyWidth, moneyHeight),
    Large(padding220, padding48), Huge(padding300, padding66),
}

@Composable
fun LabelSize.getTextStyle(): TextStyle {
    return when (this) {
        LabelSize.Small -> MaterialTheme.typography.h4
        LabelSize.Medium2 -> MaterialTheme.typography.h4
        LabelSize.Medium -> MaterialTheme.typography.h3
        LabelSize.Large -> MaterialTheme.typography.h2
        else -> MaterialTheme.typography.h1
    }
}

@Composable
fun TextLabel2(
    modifier: Modifier = Modifier,
    labelSize: LabelSize = LabelSize.Medium2,
    frame: Int = R.drawable.card_label,
    text: String,
    color: Color = Color.White,
    translateY: Dp = padding0
) {
    Box(
        modifier = modifier.size(labelSize.width, labelSize.height),
        contentAlignment = Alignment.Center
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(id = frame),
            contentScale = ContentScale.FillBounds,
            contentDescription = null
        )
        Text(
            modifier = Modifier.graphicsLayer {
                this.translationY = translateY.toPx()
            },
            text = text,
            style = labelSize.getTextStyle(),
            maxLines = 1,
            color = color
        )
    }
}