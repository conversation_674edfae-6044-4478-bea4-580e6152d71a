package com.moyu.chuanqirensheng.screen.life

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.tcg.TcgManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.screen.talent.TalentPage

@Composable
fun TalentScreen() {
    val listTabItems = listOf(
        stringResource(R.string.talent1),
        stringResource(R.string.talent2),
        stringResource(R.string.talent3),
    )
    val lifePagerState = rememberPagerState {
        listTabItems.size
    }
    GameBackground(title = stringResource(R.string.talent)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            NavigationTab(
                lifePagerState,
                listTabItems,
                listOf(false, false, false, false, TcgManager.hasRed()),
            )
            HorizontalPager(
                state = lifePagerState,
            ) { page ->
                TalentPage(page + 1)
            }
        }
    }
}