package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.google.accompanist.flowlayout.FlowRow
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.equip.canUpStar
import com.moyu.chuanqirensheng.logic.equip.getElementTypeRes
import com.moyu.chuanqirensheng.logic.equip.getQualityName
import com.moyu.chuanqirensheng.logic.equip.getQualityRes
import com.moyu.chuanqirensheng.repository.Dialogs.equipDetailDialog
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.CommonDialog
import com.moyu.chuanqirensheng.screen.effect.newTurnEffectState
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.starUpEffect
import com.moyu.chuanqirensheng.screen.more.Stars
import com.moyu.chuanqirensheng.thread.gameDispatcher
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.cardStarBigSize
import com.moyu.chuanqirensheng.ui.theme.eventCardBigHeight
import com.moyu.chuanqirensheng.ui.theme.eventCardBigWidth
import com.moyu.chuanqirensheng.ui.theme.eventCardBottomFrameHeight
import com.moyu.chuanqirensheng.ui.theme.eventTitleBigHeight
import com.moyu.chuanqirensheng.ui.theme.eventTitleBigWidth
import com.moyu.chuanqirensheng.ui.theme.gapSmall
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.equipment.Equip
import com.moyu.core.model.property.Property
import kotlinx.coroutines.launch

@Composable
fun EquipDetailDialog(show: MutableState<Equip?>) {
    show.value?.let { equip ->
        CommonDialog(
            title = "",
            frame = null,
            noPadding = true,
            clickFrame = { show.value = null },
            heightInDp = if (equip.canUpStar() || (repo.inGame.value && !equip.peek)) eventCardBigHeight * 1.60f else eventCardBigHeight * 1.2f,
            onDismissRequest = {
                show.value = null
            }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                SingleDetailEquipCard(
                    Modifier.size(eventCardBigWidth, eventCardBigHeight),
                    equip
                )
                EquipStarUpView(equip = equip)
            }
        }
    }
}

@Composable
fun SingleDetailEquipCard(modifier: Modifier, equip: Equip) {
    Box(modifier) {
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingMedium)
                .background(Color.Black),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(id = getImageResourceDrawable(equip.pic)),
            contentDescription = equip.name
        )
        FlowRow(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .padding(paddingMedium)
                .height(eventCardBigHeight / 4)
                .background(B50)
                .padding(paddingSmallPlus),
        ) {
            equip.getAllProperty().apply {
                RolePropertyLine(
                    originProperty = Property(), showBoost = true, showEmpty = false, bigItem = true
                )
            }
        }
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.main_frame_1),
            contentDescription = null
        )
        EquipTitleView(Modifier.align(Alignment.TopCenter), equip)
        EquipTypeView(
            Modifier
                .align(Alignment.TopStart)
                .graphicsLayer {
                    translationX = -paddingSmall.toPx()
                    translationY = -paddingSmall.toPx()
                    clip = false
                }, equip
        )
        EquipElementView(
            Modifier
                .align(Alignment.TopEnd)
                .padding(top = gapSmall, end = paddingLarge), equip
        )
    }
}

@Composable
fun EquipElementView(modifier: Modifier, equip: Equip) {
    EffectButton(modifier = modifier.size(imageHugeLite), onClick = {
        equip.getPartName().toast()
    }) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(id = equip.getElementTypeRes()),
            contentDescription = equip.getPartName()
        )
    }
}

@Composable
fun EquipTitleView(modifier: Modifier, equip: Equip) {
    Box(
        modifier = modifier.size(eventTitleBigWidth, eventTitleBigHeight),
        contentAlignment = Alignment.Center
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.card_label),
            contentDescription = equip.name
        )
        Text(
            modifier = Modifier.padding(bottom = paddingLarge),
            text = equip.name,
            style = MaterialTheme.typography.h3,
            textAlign = TextAlign.Center,
        )
        Stars(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = paddingSmall),
            stars = equip.star,
            starHeight = cardStarBigSize
        )
    }
}

@Composable
fun EquipTypeView(modifier: Modifier, equip: Equip) {
    EffectButton(modifier = modifier.size(imageLargePlus), onClick = {
        equip.quality.getQualityName().toast()
    }) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(id = equip.quality.getQualityRes()),
            contentDescription = equip.name
        )
        Text(
            text = stringResource(R.string.equip), style = MaterialTheme.typography.h3, maxLines = 2
        )
    }
}

@Composable
fun EquipStarUpView(modifier: Modifier = Modifier, equip: Equip) {
    Box(
        modifier = modifier.height(eventCardBottomFrameHeight)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.common_big_frame),
            contentDescription = null
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingMedium),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceAround
        ) {
            if (repo.inGame.value && !equip.peek && !repo.gameMode.value.isPvpMode()) { // 局内
                if (equip.uuid.isNotEmpty()) {
                    val button = if (equip.equipped) stringResource(R.string.destroy)
                    else if (BattleManager.equipGameData.any { it.equipped && it.part == equip.part }) stringResource(
                                            R.string.replace)
                    else stringResource(id = R.string.equip)
                    GameButton(text = button, buttonSize = ButtonSize.Small, onClick = {
                        BattleManager.putEquip(equip)
                        equipDetailDialog.value = null
                    })
                }
            } else if (equip.canUpStar()) {
                val maxStar = equip.star >= equip.starLimit
                val nextEquip = repo.gameCore.getEquipmentPool()
                    .firstOrNull { it.mainId == equip.mainId && it.star == equip.star + 1 }
                nextEquip?.getAllProperty()?.let {
                    Text(
                        text = stringResource(R.string.next_level_property), style = MaterialTheme.typography.h5
                    )
                    FlowRow(
                        modifier = Modifier
                            .padding(horizontal = paddingLarge),
                    ) {
                        it.RolePropertyLine(
                            originProperty = Property(),
                            showBoost = true,
                            showEmpty = false,
                            bigItem = true
                        )
                    }
                }
                Spacer(modifier = Modifier.size(paddingSmall))
                Text(
                    text = stringResource(id = R.string.same_card) + equip.num + "/" + equip.starUpNum, style = MaterialTheme.typography.h3
                )
                GameButton(text = if (maxStar) stringResource(id = R.string.star_max) else stringResource(id = R.string.star_up),
                    buttonStyle = ButtonStyle.Green,
                    enabled = equip.starUpNum != 0 && equip.num >= equip.starUpNum && equip.star != equip.starLimit,
                    onClick = {
                        restartEffect(newTurnEffectState, starUpEffect)
                        GameApp.globalScope.launch(gameDispatcher) {
                            equipDetailDialog.value = repo.equipManager.upgrade(equip)
                        }
                    })
            }
        }
    }
}