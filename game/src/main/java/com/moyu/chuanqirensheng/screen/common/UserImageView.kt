package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import coil.compose.rememberAsyncImagePainter
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.userHeadHeight
import com.moyu.chuanqirensheng.ui.theme.userHeadWidth
import com.moyu.chuanqirensheng.util.getImageResourceDrawable


@Composable
fun UserImageView(
    modifier: Modifier = Modifier,
    headRes: String? = null,
    name: String? = null,
    onClick: (() -> Unit)? = null,
    showEditIcon: Boolean = false
) {
    Box(modifier = modifier.let {
        if (onClick != null) it.clickable { onClick() } else it
    }) {
        Box(Modifier.align(Alignment.CenterStart)) {
            name?.let {
                Image(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .height(userHeadHeight * 2 / 3)
                        .width(userHeadWidth)
                        .graphicsLayer {
                            translationX = padding22.toPx()
                        },
                    painter = painterResource(id = R.drawable.shop_name_frame),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )
            }
            headRes?.let {
                Image(
                    // todo
                    painter = if (it.startsWith("http")) rememberAsyncImagePainter(it) else painterResource(
                        id = getImageResourceDrawable(it)
                    ),
                    contentScale = ContentScale.Crop,
                    alignment = Alignment.TopCenter,
                    modifier = Modifier
                        .align(Alignment.CenterStart)
                        .size(userHeadHeight)
                        .padding(padding6)
                        .graphicsLayer {
                            translationY = padding1.toPx()
                            translationX = -padding1.toPx()
                        }
                        .clip(RoundedCornerShape(50)).background(
                            if (it.startsWith("http")) Color.Transparent else Color.White
                        ),
                    contentDescription = null
                )
            }
            Box(modifier = Modifier
                .align(Alignment.CenterStart)
                .size(userHeadHeight)
                .graphicsLayer {
                    translationY = padding1.toPx()
                    translationX = -padding1.toPx()
                }, contentAlignment = Alignment.Center) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    painter = painterResource(id = R.drawable.hero_frame),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )
                if (showEditIcon) {
                    Image(
                        modifier = Modifier
                            .size(userHeadHeight * 0.3f).alpha(0.5f),
                        painter = painterResource(id = R.drawable.icon_setting),
                        contentScale = ContentScale.Fit,
                        contentDescription = null
                    )
                }
            }
        }
        name?.let {
            Text(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(start = userHeadHeight, bottom = padding22),
                text = name,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = MaterialTheme.typography.h2,
            )
        }
    }
}