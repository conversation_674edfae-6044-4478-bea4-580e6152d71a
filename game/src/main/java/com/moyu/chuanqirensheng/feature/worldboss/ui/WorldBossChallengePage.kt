package com.moyu.chuanqirensheng.feature.worldboss.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.worldboss.WorldBossManager
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.getImageResourceDrawable

@Composable
fun WorldBossChallengePage() {
    val todayWorldBoss = remember { WorldBossManager.getTodayWorldBoss() }
    val worldBossRace = remember { WorldBossManager.getWorldBossRace() }
    val maxChallengeNum = remember { WorldBossManager.getMaxChallengeNum() }
    val challengeNumToday = WorldBossManager.challengeNumToday.value
    val damageToday = WorldBossManager.damageToday.value
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(padding16),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(padding16)
    ) {
        // 世界Boss形象
        Box(
            modifier = Modifier
                .size(200.dp)
                .padding(padding8),
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(getImageResourceDrawable(worldBossRace.pic)),
                contentDescription = "World Boss",
                modifier = Modifier.fillMaxSize()
            )
        }
        
        // Boss名称
        Text(
            text = worldBossRace.name,
            style = MaterialTheme.typography.h1,
            color = Color.Red,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(padding8))
        
        // 挑战信息
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Text(
                    text = stringResource(R.string.world_boss_challenge_count),
                    style = MaterialTheme.typography.h3
                )
                Text(
                    text = "$challengeNumToday/$maxChallengeNum",
                    style = MaterialTheme.typography.h2,
                    color = if (challengeNumToday >= maxChallengeNum) Color.Red else Color.Green
                )
            }
            
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Text(
                    text = stringResource(R.string.world_boss_damage_today),
                    style = MaterialTheme.typography.h3
                )
                Text(
                    text = damageToday.toString(),
                    style = MaterialTheme.typography.h2,
                    color = Color.Blue
                )
            }
        }
        
        Spacer(modifier = Modifier.height(padding16))
        
        // 阵容限制提示
        todayWorldBoss?.let { boss ->
            if (boss.type.isNotEmpty()) {
                Text(
                    text = stringResource(R.string.world_boss_formation_limit),
                    style = MaterialTheme.typography.h3,
                    color = Color.Gray,
                    textAlign = TextAlign.Center
                )
                
                // 显示限制的兵种类型
                Text(
                    text = stringResource(R.string.world_boss_allowed_types, boss.type.joinToString(",")),
                    style = MaterialTheme.typography.h4,
                    color = Color.Gray,
                    textAlign = TextAlign.Center
                )
            }
        }
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 挑战按钮
        GameButton(
            text = stringResource(R.string.world_boss_challenge),
            buttonSize = ButtonSize.Big,
            enabled = WorldBossManager.canChallenge()
        ) {
            if (WorldBossManager.canChallenge()) {
                // TODO: 进入战斗界面
                GameApp.instance.getWrapString(R.string.world_boss_challenge_start).toast()
            } else {
                GameApp.instance.getWrapString(R.string.world_boss_no_challenge_left).toast()
            }
        }
    }
}
