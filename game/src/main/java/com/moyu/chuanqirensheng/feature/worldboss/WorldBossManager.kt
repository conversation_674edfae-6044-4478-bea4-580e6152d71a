package com.moyu.chuanqirensheng.feature.worldboss

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_WORLD_BOSS
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_WORLD_BOSS_CHALLENGE_NUM
import com.moyu.chuanqirensheng.sub.datastore.KEY_WORLD_BOSS_DAMAGE_TODAY
import com.moyu.chuanqirensheng.sub.datastore.KEY_WORLD_BOSS_TIME_COUNT
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.model.WorldBoss

object WorldBossManager {
    val challengeNumToday = Guarded(KEY_WORLD_BOSS_CHALLENGE_NUM)
    val damageToday = Guarded(KEY_WORLD_BOSS_DAMAGE_TODAY)
    
    fun init() {
        if (isNetTimeValid()) {
            if (!isSameDay(getLongFlowByKey(KEY_WORLD_BOSS_TIME_COUNT), getCurrentTime())) {
                setLongValueByKey(KEY_WORLD_BOSS_TIME_COUNT, getCurrentTime())
                challengeNumToday.value = 0
                damageToday.value = 0
            }
        }
    }
    
    fun getMaxChallengeNum(): Int {
        return try {
            repo.gameCore.getValueById(87).toInt()
        } catch (e: Exception) {
            5 // 默认每日5次挑战
        }
    }

    fun getForbiddenAllyIds(): List<Int> {
        return try {
            repo.gameCore.getValueById(88).split(",").map { it.toInt() }
        } catch (e: Exception) {
            emptyList() // 默认没有禁止上阵的英雄
        }
    }
    
    fun getTodayWorldBoss(): WorldBoss? {
        val currentDay = getCurrentDay()
        return repo.gameCore.getWorldBossByDay(currentDay)
    }
    
    private fun getCurrentDay(): Int {
        // 获取开服天数，从第1天开始，超过365天后循环
        // 使用loginData中的时间作为开服时间基准
        val serverStartTime = GameApp.instance.loginData.value.time
        val daysSinceStart = if (serverStartTime > 0) {
            ((getCurrentTime() - serverStartTime) / (24 * 60 * 60 * 1000)).toInt() + 1
        } else {
            1 // 默认第1天
        }
        return ((daysSinceStart - 1) % 365) + 1
    }
    
    fun getWorldBossRace() = repo.gameCore.getRaceById(118201)
    
    fun canChallenge(): Boolean {
        return challengeNumToday.value < getMaxChallengeNum()
    }
    
    fun addDamage(damage: Int) {
        damageToday.value += damage
        challengeNumToday.value += 1
    }
    
    fun unlocked(): Boolean {
        val unlock = repo.gameCore.getUnlockById(UNLOCK_WORLD_BOSS)
        return UnlockManager.getUnlockedFlow(unlock)
    }
    
    fun hasRed(): Boolean {
        return unlocked() && canChallenge()
    }
    
    fun isServerDay8OrLater(): Boolean {
        val serverStartTime = GameApp.instance.loginData.value.time
        val daysSinceStart = if (serverStartTime > 0) {
            ((getCurrentTime() - serverStartTime) / (24 * 60 * 60 * 1000)).toInt() + 1
        } else {
            1
        }
        return daysSinceStart >= 8
    }

    fun hasMultipleServers(): Boolean {
        return GameApp.instance.loginData.value.serverList.size > 1
    }
    
    fun shouldShowCrossServerRank(): Boolean {
        return isServerDay8OrLater() && hasMultipleServers()
    }
}
