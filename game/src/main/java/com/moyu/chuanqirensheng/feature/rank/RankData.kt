package com.moyu.chuanqirensheng.feature.rank

import kotlinx.serialization.Serializable

@Serializable
data class RankWrapper(
    val data: String = "", // 加密后的数据
)

@Serializable
data class RankData(
    val time: Long,
    val versionCode: Int = 0,
    val userName: String = "",
    val userId: String = "",
    val userPic: String = "",
    val endingNum: Int = 0,
    val talentLevel: Int = 0,
    val tcgValue: Int = 0,
    val electric: Int = 0,
    val level: Int = 0,
    val liked: Int = 0,
    val pvpScore: Int = 0,
    val pvpLastScore: Int = 0,
    val pvpData: PvpData = PvpData(),
    val platformChannel: String = "taptap",
    val holidayNum: Int = 0,
    val towerLevel: Int = 0,
    val serverId: Int = 0, // 服务器ID
    val worldBossDamage: Int = 0, // 对世界boss产生的伤害
)

@Serializable
data class PvpData(
    val allyIds: List<Int> = emptyList(),
    val talentIds: Map<Int, Int> = emptyMap(),
    val win: Int = 0,
    val lose: Int = 0,
)
