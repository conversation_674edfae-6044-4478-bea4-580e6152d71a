package com.moyu.chuanqirensheng.feature.draw.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.draw.DrawManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.ButtonType
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.effect.ForeverGif
import com.moyu.chuanqirensheng.screen.effect.GifView
import com.moyu.chuanqirensheng.screen.effect.drawGif
import com.moyu.chuanqirensheng.screen.effect.packageGif
import com.moyu.chuanqirensheng.screen.resource.HeroCouponPoint
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.shopItemWidth
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun HeroCouponItem() {
    Row(
        Modifier.fillMaxWidth().padding(start = padding60),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        val gifEnabled = remember {
            mutableStateOf(false)
        }
        Box(Modifier.size(ItemSize.LargePlus.frameSize)) {
            if (Dialogs.drawResultDialog.value == null) {
                ForeverGif(
                    modifier = Modifier
                        .fillMaxSize()
                        .scale(1.5f),
                    resource = packageGif.gif,
                    num = packageGif.count,
                    needGap = false
                )
            }
            Image(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .graphicsLayer {
                        translationY = padding22.toPx()
                    },
                contentScale = ContentScale.FillWidth,
                painter = painterResource(id = R.drawable.shop_draw_frame),
                contentDescription = null
            )
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding8),
                painter = painterResource(id = R.drawable.hero_coupon),
                contentDescription = null
            )
            GifView(
                modifier = Modifier.width(shopItemWidth),
                enabled = gifEnabled.value,
                gifCount = drawGif.count,
                gifDrawable = drawGif.gif
            ) {
                gifEnabled.value = false
            }
        }
        Column(Modifier.weight(1f).graphicsLayer {
            translationY = padding22.toPx()
        }, horizontalAlignment = Alignment.CenterHorizontally) {
            HeroCouponPoint()
            Spacer(modifier = Modifier.height(padding12))
            GameButton(text = stringResource(R.string.coupon),
                buttonSize = ButtonSize.MediumMinus,
                buttonStyle = ButtonStyle.Orange,
                buttonType = ButtonType.Normal,
                enabled = (AwardManager.couponHero.value + AwardManager.key.value / repo.gameCore.getHeroCouponRate()) >= 10,
                onClick = {
                    if ((AwardManager.couponHero.value + AwardManager.key.value / repo.gameCore.getHeroCouponRate()) < 10) {
                        GiftManager.onDrawHeroFailed()
                        GameApp.instance.getWrapString(R.string.hero_coupon_not_enough).toast()
                    } else {
                        gifEnabled.value = true
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            DrawManager.buyHeroCoupon()
                        }
                    }
                })
        }
    }
}