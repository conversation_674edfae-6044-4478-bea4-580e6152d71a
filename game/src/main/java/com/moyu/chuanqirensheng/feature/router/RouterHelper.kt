package com.moyu.chuanqirensheng.feature.router

import androidx.navigation.NavHostController
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.killSelf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

const val PAGE_PARAM_TAB_INDEX = "initTabIndex"

const val LOGIN_SCREEN = "login"
const val MORE_SCREEN = "more"
const val CREATE_GAME_SCREEN = "create_game"

const val OUT_HERO = "out_hero"
const val OUT_ALLY = "out_ally"
const val TALENT_SCREEN = "talent"
const val SELL_SCREEN = "sell"
const val QUEST_SCREEN = "quest"
const val SIGN_SCREEN = "sign"
const val BATTLE_PASS_SCREEN = "battle_pass"
const val BATTLE_PASS2_SCREEN = "battle_pass2"
const val NEW_TASK_SCREEN = "new_task"
const val SEVEN_DAY_SCREEN = "seven_day"

const val VIP_SCREEN = "vip"
const val DRAW_SCREEN = "draw"
const val RANK_SCREEN = "rank"
const val ENDING_SCREEN = "ending"
const val SKILL_ILLUSTRATION_SCREEN = "skill_illustration"
const val ADVANCED_TUTOR_SCREEN = "advanced_tutor"
const val PVP_SCREEN = "pvp"
const val PVP2_SCREEN = "pvp2"
const val PVP_SELL_SCREEN = "pvp_sell"
const val PVP_RANK_SCREEN = "pvp_rank"
const val PVP2_RANK_SCREEN = "pvp2_rank"
const val PVP_CHOOSE_ENEMY_SCREEN = "pvp_choose_enemy"
const val PVP2_CHOOSE_ENEMY_SCREEN = "pvp2_choose_enemy"
const val PVP_QUEST_SCREEN = "pvp_quest"
const val PVP2_QUEST_SCREEN = "pvp2_quest"
const val PVP_BATTLE_SCREEN = "pvp_battle"
const val PVP2_BATTLE_SCREEN = "pvp2_battle"

const val LOTTERY_SCREEN1 = "lottery_screen1"
const val LOTTERY_SCREEN2 = "lottery_screen2"

const val TOWER_SCREEN = "tower_screen"
const val TOWER_BATTLER_SCREEN = "tower_battle_screen"

const val WORLD_BOSS_SCREEN = "world_boss_screen"

// 游戏调试
const val DEBUG_SCREEN = "debug_game"
const val DEBUG_BATTLE = "debug_battle"

// 事件
const val EVENT_SELECT_SCREEN = "event_select"
const val EVENT_DETAIL_SCREEN = "event_detail"

const val SELL_SCREEN_PREFIX = "sell/"
const val SELL_SCREEN_RAW = "sell/$PAGE_PARAM_TAB_INDEX"

const val HOLIDAY_SCREEN = "holiday"
const val DUNGEON_ALL_SCREEN = "dungeon_all"
const val ACTIVITY_ALL_SCREEN = "activity_all"

const val MAILS_SCREEN = "mails"

fun NavHostController.toSingleInstance(route: String) {
    ReportManager.onPage(route)
    if (!isNetTimeValid()) {
        GameApp.instance.getWrapString(R.string.login_status_error_tips).toast()
    } else {
        GameApp.globalScope.launch(Dispatchers.Main) {
            if (!popBackStack(route.getRawRoute(), false)) {
                navigate(route)
            }
        }
    }
}


private fun String.getRawRoute(): String {
    return if (this.startsWith(SELL_SCREEN_PREFIX)) {
        SELL_SCREEN_RAW
    } else {
        this
    }
}

fun isCurrentRoute(route: String): Boolean {
    return GameApp.instance.navController?.currentDestination?.route?.equals(
        route
    ) == true
}

fun popTop() {
    GameApp.instance.navController?.currentDestination?.route?.let {
        if (it == LOGIN_SCREEN) {
            Dialogs.alertDialog.value = CommonAlert(
                content = GameApp.instance.getWrapString(R.string.quit_game_toast),
                onConfirm = {
                    GameApp.instance.quitGame() {
                        killSelf()
                    }
                })
        } else {
            GameApp.globalScope.launch(Dispatchers.Main) {
                GameApp.instance.navController?.popBackStack(it, true)
            }
        }
    }
}

fun popScreen(route: String) {
    GameApp.globalScope.launch(Dispatchers.Main) {
        GameApp.instance.navController?.popBackStack(route, true)
    }
}

val needVerifyRoutes = listOf(
    SELL_SCREEN,
)

fun goto(route: String) {
    if (!isNetTimeValid()) {
        (GameApp.instance.getWrapString(R.string.time_error_tips)).toast()
    } else if (GameApp.instance.getObjectId().isNullOrEmpty()) {
        (GameApp.instance.getWrapString(R.string.userid_error_tips)).toast()
    } else {
        GameApp.instance.navController?.toSingleInstance(route)
    }
}

fun gotoQuest() {
    goto(QUEST_SCREEN)
}

fun pop(route: String) {
    GameApp.instance.navController?.popBackStack(route.getRawRoute(), true)
}

fun gotoSellWithTabIndex(tabIndex: Int = 0) {
    pop(SELL_SCREEN)
    goto("$SELL_SCREEN_PREFIX$tabIndex")

    // 这里是跳转商店，购买魔晶，可能打开着几个弹窗，需要关闭
    Dialogs.allyStarUpDialog.value = null
    Dialogs.allyDetailDialog.value = null
}