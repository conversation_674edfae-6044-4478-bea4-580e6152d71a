package com.moyu.chuanqirensheng.feature.worldboss.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.rank.CROSS_WORLD_BOSS_TYPE
import com.moyu.chuanqirensheng.feature.rank.LAST_CROSS_WORLD_BOSS_TYPE
import com.moyu.chuanqirensheng.feature.rank.LAST_WORLD_BOSS_TYPE
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.WORLD_BOSS_TYPE
import com.moyu.chuanqirensheng.feature.rank.ui.RankPage
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.util.isNetTimeValid
import kotlinx.coroutines.delay
import timber.log.Timber

@Composable
fun WorldBossRankPage(
    isRealtime: Boolean,
    isCrossServer: Boolean
) {
    val rankData = remember { mutableStateOf(emptyList<RankData>()) }
    
    LaunchedEffect(isRealtime, isCrossServer) {
        try {
            if (isNetTimeValid()) {
                delay(200)
                // TODO: 实现排行榜数据获取
                // 这里应该调用API获取排行榜数据
                // val type = when {
                //     isCrossServer && isRealtime -> CROSS_WORLD_BOSS_TYPE
                //     isCrossServer && !isRealtime -> LAST_CROSS_WORLD_BOSS_TYPE
                //     !isCrossServer && isRealtime -> WORLD_BOSS_TYPE
                //     else -> LAST_WORLD_BOSS_TYPE
                // }
                // getRanks(GameApp.instance.resources.getString(R.string.platform_channel), type).let {
                //     rankData.value = json.decodeFromString(ListSerializer(RankData.serializer()), it.message)
                // }
            }
        } catch (e: Exception) {
            Timber.e(e)
            GameApp.instance.getWrapString(R.string.net_error_retry).toast()
        }
    }
    
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
            Text(
                modifier = Modifier.padding(start = padding12),
                text = if (isRealtime) {
                    stringResource(R.string.world_boss_rank_realtime_tips)
                } else {
                    stringResource(R.string.world_boss_rank_final_tips)
                },
                style = MaterialTheme.typography.h3
            )
            Spacer(modifier = Modifier.weight(1f))
            
            if (isRealtime) {
                // 实时排行榜显示查看奖励按钮
                GameButton(
                    text = stringResource(R.string.awards), 
                    buttonSize = ButtonSize.MediumMinus
                ) {
                    if (isCrossServer) {
                        Dialogs.worldBossCrossRankAwardDialog.value = true
                    } else {
                        Dialogs.worldBossRankAwardDialog.value = true
                    }
                }
            } else {
                // 定榜排行榜显示领取奖励按钮
                GameButton(
                    text = stringResource(R.string.gain_award),
                    buttonSize = ButtonSize.MediumMinus
                ) {
                    if (isCrossServer) {
                        Dialogs.worldBossCrossRankGetAwardDialog.value = true
                    } else {
                        Dialogs.worldBossRankGetAwardDialog.value = true
                    }
                }
            }
        }
        
        // 排行榜内容
        val rankType = when {
            isCrossServer && isRealtime -> CROSS_WORLD_BOSS_TYPE
            isCrossServer && !isRealtime -> LAST_CROSS_WORLD_BOSS_TYPE
            !isCrossServer && isRealtime -> WORLD_BOSS_TYPE
            else -> LAST_WORLD_BOSS_TYPE
        }
        
        RankPage(type = rankType, data = rankData) { rankData, rankIndex ->
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Text(
                    text = stringResource(R.string.world_boss_damage) + "：" + rankData.worldBossDamage,
                    style = MaterialTheme.typography.h3
                )
                Spacer(modifier = Modifier.size(padding12))
                Text(
                    text = stringResource(R.string.server_id) + "：" + rankData.serverId,
                    style = MaterialTheme.typography.h5
                )
            }
        }
    }
}
