package com.moyu.chuanqirensheng.feature.mission

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateMapOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_MISSION_GAINED
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.toAward
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.model.award.GuardedB
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.core.model.Mission


object MissionManager {
    private val gainedMap = mutableStateMapOf<Int, MutableState<Boolean>>()

    fun init() {
        repo.gameCore.getMissionPool().forEach {
            gainedMap[it.id] = GuardedB(KEY_MISSION_GAINED + it.id)
        }
    }

    suspend fun gain(mission: Mission) {
        val gained = gainedMap[mission.id]!!
        if (gained.value) {
            GameApp.instance.getWrapString(R.string.already_got).toast()
            return
        }
        gained.value = true
        setBooleanValueByKey(KEY_MISSION_GAINED + mission.id, true)
        val award = mission.toAward()
        Dialogs.awardDialog.value = award
        AwardManager.gainAward(award)
    }

    fun getMissionByIndex(index: Int): Mission {
        return repo.gameCore.getMissionPool()[index]
    }

    fun isMissionGainedByIndex(index: Int): Boolean {
        return gainedMap[getMissionByIndex(index).id]?.value ?: false
    }

    fun hasRed(): Boolean {
        return repo.gameCore.getMissionPool().mapIndexed { index, mission ->
            val questList = TaskManager.getNewQuestsByIndex(index)
            val missionsAllDone = questList.all { it.done } && questList.isNotEmpty()
            val awardGained = isMissionGainedByIndex(index)
            missionsAllDone && !awardGained
        }.any { it }
    }

    fun getInitPageIndex(): Int {
        repo.gameCore.getMissionPool().forEachIndexed { index, mission ->
            val questList = TaskManager.getNewQuestsByIndex(index)
            val missionsAllDone = questList.all { it.done } && questList.isNotEmpty()
            val awardGained = isMissionGainedByIndex(index)
            if (missionsAllDone && !awardGained) {
                return index
            }
        }
        return 9999
    }
}