package com.moyu.chuanqirensheng.sub.loginsdk

import androidx.activity.ComponentActivity
import androidx.compose.runtime.MutableState

/**
 * 隔离游戏sdk能力，后续可能会替换成非tap sdk版本
 */
interface GameSdkProcessor {
    fun initGameSdk()
    fun login(activity: ComponentActivity)
    fun initSDK(activity: ComponentActivity)
    fun antiAddictPassed(): MutableState<Boolean>
    fun hasLogin(): Boolean
    fun getAvatarUrl(): String?
    fun getUserName(): String?
    fun getObjectId(): String?
    fun getAntiAddictionContent(): String
    fun checkAntiAddiction(activity: ComponentActivity)
    fun dealAfterLogin(name: String, id: String, avatarUrl: String, activity: ComponentActivity)
    fun isAgeUnder8(): Boolean
    fun isAgeIn8To16(): Boolean
    fun quitGame(onExit: ()->Unit)
    fun uploadRoleInfo()
}