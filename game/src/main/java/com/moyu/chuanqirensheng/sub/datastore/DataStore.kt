package com.moyu.chuanqirensheng.sub.datastore

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.moyu.core.DS_NAME
import com.moyu.core.DS_NAME2
import com.moyu.core.DS_NAME3

val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = DS_NAME)
val Context.dataStore2: DataStore<Preferences> by preferencesDataStore(name = DS_NAME2)
val Context.dataStore3: DataStore<Preferences> by preferencesDataStore(name = DS_NAME3)

// gift
const val KEY_GIFT_AWARDED = "qnxp0_"
const val KEY_DISPLAY_GIFT = "gipxm"
const val KEY_DIED_IN_GAME = "x98ip"
const val KEY_KEY_NOT_ENOUGH = "lo88ip"
const val KEY_DIAMOND_NOT_ENOUGH = "lo367p"
const val KEY_PVP_DIAMOND_NOT_ENOUGH = "lo367pj33"
const val KEY_CONSUME_ALLY_COUPON = "lo3mm9"
const val KEY_CONSUME_HERO_COUPON = "lo3mx1"
const val KEY_CONSUME_ALLY_COUPON_ALL = "lo3mm9a"
const val KEY_CONSUME_HERO_COUPON_ALL = "lo3mx1a"
const val KEY_DIED_IN_PVP = "lo52ip"

// 存档
const val KEY_FIRST_CHARGE_DONE = "qnmx"
const val KEY_FIRST_CHARGE_AWARDED = "qnxp0"

const val KEY_MAP_AWARDED = "jsla*#_"

const val KEY_RECORD = "qn"
const val KEY_RECORD_EVENT = "qq"
const val KEY_GAME_COUNT = "gckc"
const val KEY_GAME_TIME_COUNT = "1cxk"
const val KEY_GAME_LOGIN_DAY = "ks8x"
const val KEY_SELECTED_DIFFICULT = "kp08"
const val KEY_SELECTED_MAP = "k2p08"
const val KEY_GAME_ENDING_DIAMOND_COUNT = "lqz8"
const val KEY_FIRST_ENDING_AWARD = "ss31x"
const val KEY_REPUTATION_GAINED = "mihx2"
const val KEY_MAX_DONE_DIFFICULT = "xxx1dsa"
const val KEY_MAX_DONE_MAP = "xxx1oit"

// 版本兼容
const val KEY_VERIFIED = "ab"
const val KEY_OBJECT_ID = "kp"
const val KEY_USER_NAME_CHANGED = "unc"
const val KEY_CUSTOM_USER_NAME = "cun"
const val KEY_CUSTOM_AVATAR = "cav"

// 系统设置
const val KEY_MUTE_MUSIC = "ac"
const val KEY_MUTE_SOUND = "ad"
const val KEY_MUTE_DIALOG = "xadx"
const val KEY_SPEED = "ae"
const val KEY_INVISIBLE_IN_RANK = "dda"
const val KEY_TEXT_FIXED_COLOR = "xijd"
const val KEY_HERO_AUTO = "mo97"

// 游戏记录
const val KEY_NEED_SHOW_PRIVACY = "af"
const val KEY_NEED_SHOW_PERMISSION = "ag"
const val KEY_NEW_USER = "ah"
const val KEY_NEWEST_VERSION = "ap1"
const val KEY_ENDING_NUM = "i7"
const val KEY_MAX_LEVEL = "iz30"
const val KEY_PAY_DATA = "xx981"

// 各种材料资源铜钱
const val KEY_DIAMOND = "an"
const val KEY_DIAMOND_ALL = "kkls"
const val KEY_WAR_PASS = "w8l"
const val KEY_WAR_PASS2 = "w8xxl"
const val KEY_WAR_PASS3 = "wkxxl"
const val KEY_CHEATING = "p0k1"
const val KEY_KEY = "ka"
const val KEY_COUPON_ALLY = "x0jxs1"
const val KEY_COUPON_HERO = "x0jjs"
const val KEY_COUPON_HISTORY = "x0jxs3"
const val KEY_KEY_ALL = "2kls"
const val KEY_ELECTRIC = "k2"
const val KEY_REAL_MONEY = "kxj2"
const val KEY_PVP_DIAMOND = "nkx9"
const val KEY_PVP_SCORE = "nkxx9"
const val KEY_PVP2_SCORE = "nkxx9d2"
const val KEY_PVP_LAST_DAY_SCORE = "nk1xx9"
const val KEY_PVP_DIAMOND_ALL = "zznkx9"
const val KEY_AD_NUM = "aodnm"

const val KEY_KEY_COST = "kan8"

const val KEY_ELECTRIC1 = "k9mn1"
const val KEY_ELECTRIC2 = "k9mn2"
const val KEY_ELECTRIC3 = "k9mn3"
const val KEY_ELECTRIC4 = "k9mn4"
const val KEY_ELECTRIC5 = "k9mn5"
const val KEY_ELECTRIC6 = "k9mn6"
const val KEY_ELECTRIC7 = "k9mn7"

const val KEY_COLLECT_TASK = "k99p3"
const val KEY_COST_TASK = "k9lx3"
const val KEY_CHARGE_TASK = "k12p3"
const val KEY_INIT_GAME_TIME = "p82p3"
const val KEY_NEW_TASK_SELLS = "pmy63"
const val KEY_LOTTERY_SELLS = "pxj73"


// 天赋相关
const val KEY_TALENT_LEVEL = "bb"

// 卷轴技能
const val KEY_SCROLL_SKILLS = "by"
const val KEY_HEROES = "ksi"

// 军团卡
const val KEY_ALLIES = "li"

// tcg
const val KEY_TCG_CARDS = "bz"
const val KEY_TCG_CARD_REWARD = "ca"

// 成就
const val KEY_ENDINGS = "de"
const val KEY_STORY_SELECTION = "d0"

// 引导
const val KEY_GUIDE_INDEX = "cd"
const val KEY_BATTLE_DONE = "c1"
const val KEY_NON_BATTLE_DONE = "c2"

// 登录结果
const val KEY_LOGIN_MESSAGE_ID = "ua"

// ad 0是免广告，其他是配置在unlock里的
const val KEY_UNLOCK_EVIDENCE = "cc"
const val KEY_BADGES = "koxu"
const val KEY_REPUTATIONS = "llopx"
const val KEY_SKINS = "kosb"
const val KEY_SELECTED_SKINS = "ko2sb"

// 商城礼包
const val KEY_SELL_ITEMS = "io"
const val KEY_SELL_ITEM1_REFRESHED = "r3"
const val KEY_SELL_ITEM2_REFRESHED = "r2"
const val KEY_SELL_ITEMS_UPDATE_TIME_IN_MILLIS = "ii"


// 任务
const val KEY_GAME_TASK = "hy"
const val KEY_GAME_PVP_TASK = "hx21y"
const val KEY_GAME_PVP2_TASK = "hx21yxj3"
const val KEY_TASK_DIALOG_SHOWED = "ko98x"
const val KEY_ONE_TIME_GAME_TASK = "lo"
const val KEY_GAME_TASK_UPDATE_TIME_IN_MILLIS = "ay"
const val KEY_GAME_PVP_TASK_UPDATE_TIME_IN_MILLIS = "axy1"
const val KEY_GAME_PVP2_TASK_UPDATE_TIME_IN_MILLIS = "axy1x321"
const val KEY_GAME_WARPASS_TASK_UPDATE_TIME_IN_MILLIS = "kl08"
const val KEY_GAME_WARPASS2_TASK_UPDATE_TIME_IN_MILLIS = "kl08xk"
const val KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS = "0knv"
const val KEY_GAME_TASK_PROGRESS = "az" // todo 这个会导致只要是az开头的都被删除了
const val KEY_WAR_PASS_TASK = "yh9"
const val KEY_WAR_PASS2_TASK = "yh9xik"
const val KEY_NEW_TASK = "k9r3"

const val KEY_ENDING_DONE = "t3"


// cheat
const val KEY_VIP_GAINED = "1p_"
const val KEY_DRAW_GAINED = "1xxp_"
const val KEY_BATTLE_PASS_GAINED = "xxp_"
const val KEY_BATTLE_PASS_SEASON = "ouo_"

const val KEY_BATTLE_PASS2_GAINED = "xx2p_"
const val KEY_BATTLE_PASS2_SEASON = "ou2o_"

// share
const val KEY_SHARE_AWARD_IMAGE = "xk9)"
const val KEY_SHARE_AWARD_TEXT = "mo7B"

const val KEY_SIGN_GAINED = "mq1m_"
const val KEY_SHOW_SIGN_DAY = "mo0s"
const val KEY_MISSION_GAINED = "x3xmm_"

// share
const val KEY_SHARE_AWARD_COUNT = "xk1)"
const val KEY_SHARE_AWARD_GAINED1 = "xk2)"
const val KEY_SHARE_AWARD_GAINED2 = "ix24"
const val KEY_OTHER_USE_YOUR_CODE_GAINED_COUNT = "s9kl"
const val KEY_SHARE_TIME_STAMP = "1x4k"

// 名人榜
const val KEY_LIKE_TIME = "bx8L"
const val KEY_LIKE = "oppb"

// 图鉴
const val KEY_ILLUSTRATE_ALLY = "kiay"
const val KEY_ILLUSTRATE_SKILL1 = "kias1"
const val KEY_ILLUSTRATE_SKILL2 = "kias2"
const val KEY_ILLUSTRATE_SKILL3 = "kias3"
const val KEY_ILLUSTRATE_SKILL4 = "kias4"

// pvp
const val KEY_PVP_MONEY = "xxjd"
const val KEY_PVP_VALUE = "xjx1"
const val KEY_PVP_VALUE_CLEAR_TIME_IN_MILLIS = "x6qwn"
const val KEY_PVP_WIN = "xdx0"
const val KEY_PVP_LOSE = "xc"
const val KEY_PVP_WIN_TODAY = "xr1o"
const val KEY_PVP_LOSE_TODAY = "xssp"
const val KEY_PK_NUM = "xxea"
const val KEY_PK_TARGET = "xpph"
const val KEY_PK_ALLY_IDS = "xp0oh"
const val KEY_PK_UPDATE_TIME_IN_MILLIS = "fjxf"

const val KEY_PVP2_WIN = "xdx0d2"
const val KEY_PVP2_LOSE = "xcd2"
const val KEY_PVP2_WIN_TODAY = "xr1od2"
const val KEY_PVP2_LOSE_TODAY = "xsspd2"
const val KEY_PK2_NUM = "xxead2"
const val KEY_PK2_TARGET = "xpphd2"
const val KEY_PK2_ALLY_IDS = "xp0ohd2"
const val KEY_PK2_UPDATE_TIME_IN_MILLIS = "fjxfd2"

const val KEY_PVP_EQUIP_CHEST = "xtur"
const val KEY_PVP_SKILL_CHEST = "xyds"
const val KEY_PVP_CHEST = "xzxt"
const val KEY_PVP_STONE = "p8wa"
const val KEY_PVP_REFRESH_COUNT = "wf3x"
const val KEY_PVP_EQUIP_CHEST_UPDATE_TIME_IN_MILLIS = "xxxu"
const val KEY_PVP_SKILL_CHEST_UPDATE_TIME_IN_MILLIS = "xoqv"
const val KEY_PVP_CHEST_UPDATE_TIME_IN_MILLIS = "xb4w"
const val KEY_PVP_STONE_UPDATE_TIME_IN_MILLIS = "wssb"
const val KEY_PVP_REFRESH_UPDATE_TIME_IN_MILLIS = "wjkw"


// report
const val KEY_REPORT_AF_AD = "afad"
const val KEY_REPORT_AF_PURCHASE1 = "afpuch1"
const val KEY_REPORT_AF_PURCHASE2 = "afpuch2"
const val KEY_REPORT_AF_SECOND_LOGIN = "aflogin2"


// rank star
const val KEY_RANK_STAR = "rsiks2"

const val KEY_HIDE_7DAY_ICON = "pmxt3"


const val KEY_LANGUAGE = "language"
const val KEY_INIT_LANGUAGE = "init_language"


const val KEY_ONE_TURN_CONSUME_ALLY_COUPON = "xp2mm9"
const val KEY_ONE_TURN_CONSUME_HERO_COUPON = "xp2mx1"



// lottery
const val KEY_LOTTERY_CHEAP_NUM = "loxxjn1"
const val KEY_LOTTERY_EXPENSIVE_NUM = "loxxjn2"
const val KEY_LOTTERY_INIT_TIME_IN_A_WEEK = "p12e3"
const val KEY_LOTTERY_INIT_RAW_TIME = "pb2ez"
const val KEY_LOTTERY_WEEK_NUM = "p95e3"
const val KEY_LOTTERY_CHEAP_BOUGHT = "w90l3"
const val KEY_LOTTERY_EXPENSIVE_BOUGHT = "w90l1"
const val KEY_RESET_CHEAP_LOTTERY_NUM = "w90lmn1"
const val KEY_RESET_EXPENSIVE_LOTTERY_NUM = "w90lmnx"
const val KEY_FREE_CHEAP_LOTTERY_DONE = "a0p6x1"
const val KEY_FREE_EXPENSIVE_LOTTERY_DONE = "a0p6x2"
const val KEY_LOTTERY_MONEY = "a0byx1"

// holiday
const val KEY_HOLIDAY_GAME_TASK = "skjsyj1"
const val KEY_LOTTERY_HOLIDAY_NUM = "sk4mtr"
const val KEY_LOTTERY_HOLIDAY_TOTAL = "dhu245"
const val KEY_LOTTERY_HOLIDAY_BOUGHT = "bcu20"
const val KEY_RESET_HOLIDAY_LOTTERY_NUM = "nxu72"
const val KEY_FREE_HOLIDAY_LOTTERY_DONE = "xukrp0"
const val KEY_HOLIDAY_MONEY = "xjlk32"
const val KEY_HOLIDAY_SIGN_GAINED = "dukl23"
const val KEY_HOLIDAY_MISSION_GAINED = "x9n22jd"
const val KEY_HOLIDAY_SELLS = "iex3ncvu"

// tower
const val KEY_MAX_TOWER_LEVEL = "nxtows1"
const val KEY_GAINED_TOWER_LEVELS = "nxtowsx3"
const val KEY_TOWER_ALLY_IDS = "nxtosj22"

// world boss
const val KEY_WORLD_BOSS_CHALLENGE_NUM = "wbChallengeNum"
const val KEY_WORLD_BOSS_DAMAGE_TODAY = "wbDamageToday"
const val KEY_WORLD_BOSS_TIME_COUNT = "wbTimeCount"

const val KEY_MONTH_CARD_INFO = "month_in1"


const val KEY_DRAW_ALLY_FAILED = "sdi2jc99"
const val KEY_DRAW_HERO_FAILED = "sdi2jc19"
const val KEY_UNLOCK_DRAW_ID_PREFIX = "inx873_"

const val KEY_SERVER_ID = "sexki2kd"

// mails
const val KEY_READ_MAIL_IDS = "9jls82m"