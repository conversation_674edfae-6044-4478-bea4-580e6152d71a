package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.event.createRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.event.BattleLayout
import com.moyu.core.logic.enemy.DefaultRoleCreator
import com.moyu.core.logic.identifier.PlayerIdentifier
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.event.Event
import com.moyu.core.util.RANDOM

const val GOD_BATTLE = 26
class GodBattlePlayHandler(
    override val skipWin: Boolean = false, override val playId: Int = GOD_BATTLE
): PlayHandler() {
    val godAlly = mutableStateOf<Ally?>(null)

    @Composable
    override fun Layout(event: Event) {
        BattleLayout(event = event)
    }

    override fun onEventSelect(event: Event) {
        // 决斗，普通战斗,宿敌战斗,诅咒战斗
        val pool = repo.gameCore.getPoolById(event.playPara1.first())
        val enemies = pool.pool.map {
            repo.gameCore.getRaceById(it)
        }.map {
            createRole(it, event)
        }.toMutableList()
        repo.setCurrentEnemies(enemies)

        val poolGod = repo.gameCore.getPoolById(event.playPara2.first().toInt())
        godAlly.value = poolGod.pool.map { poolValue ->
            repo.gameCore.getAllyPool().first { it.id == poolValue }
        }.shuffled(RANDOM).first().apply {
            BattleManager.selectAllyToBattle(this.copy(extraInfo = GameApp.instance.getWrapString(R.string.fight_together), roleIdentifier = PlayerIdentifier()), temp = true)
        }
    }
}