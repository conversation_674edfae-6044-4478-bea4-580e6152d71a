package com.moyu.chuanqirensheng.logic.event

import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.logic.enemy.DefaultRoleCreator
import com.moyu.core.model.event.Event
import com.moyu.core.model.property.Property
import com.moyu.core.model.race.Race
import com.moyu.core.model.role.Role

/**
 * 困难模式所有敌人提高的物攻属性
 * 困难模式所有敌人提高的物防属性
 * 困难模式所有敌人提高的魔攻属性
 * 困难模式所有敌人提高的魔防属性
 * 困难模式所有敌人提高的生命属性
 * 困难模式敌人精英技能数量
 * 困难模式敌人精英技能池
 */
fun createRole(race: Race, event: Event): Role {
    val isDifficult = StoryManager.isCurrentDifficultMode()
    val extraPhysicAttack = if (isDifficult) event.hardAttribute1 else 0
    val extraPhysicDefense = if (isDifficult) event.hardAttribute2 else 0
    val extraMagicAttack = if (isDifficult) event.hardAttribute3 else 0
    val extraMagicDefense = if (isDifficult) event.hardAttribute4 else 0
    val extraHp = if (isDifficult) event.hardAttribute5 else 0
    val diffProperty = event.getDiffProperty() + Property(
        physicAttack = extraPhysicAttack,
        physicDefense = extraPhysicDefense,
        magicAttack = extraMagicAttack,
        magicDefense = extraMagicDefense,
        hp = extraHp,
    )
    val extraHard = if (isDifficult) event.hardElitePool.filterNot { it == 0 } else emptyList()
    val extraChallenge =
        if (repo.isChallenge.value) BattleManager.getTodayChallenge().effectBattle else emptyList()
    return DefaultRoleCreator.create(
        race,
        diffProperty,
        extraHard + extraChallenge
    )
}