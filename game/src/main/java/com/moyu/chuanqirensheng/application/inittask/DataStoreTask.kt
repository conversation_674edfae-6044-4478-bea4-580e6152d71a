package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.quest.FOREVER
import com.moyu.chuanqirensheng.feature.quest.QuestEvent
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.media.VideoPlayerManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.effect.newTurnEffectState
import com.moyu.chuanqirensheng.screen.effect.turnEffect
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.KEY_MUTE_MUSIC
import com.moyu.chuanqirensheng.sub.datastore.KEY_MUTE_SOUND
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEWEST_VERSION
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEW_USER
import com.moyu.chuanqirensheng.sub.datastore.KEY_SPEED
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.mapData
import com.moyu.chuanqirensheng.sub.datastore.removeKeysSync
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.job.JobContent
import com.moyu.chuanqirensheng.sub.privacy.PrivacyManager
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.core.model.toAward
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID


class DataStoreTask : JobContent<GameApp> {
    override fun execute(context: GameApp) {
        runBlocking {
            // todo 非常奇怪的一个闪退，先暂时这么保护下
            // Reading a state that was created after the snapshot was taken or in a snapshot that has not yet been applied
            newTurnEffectState.value = turnEffect

            VideoPlayerManager.init()

            GameApp.newUser = getBooleanFlowByKey(KEY_NEW_USER, true)

            val newestVersion = getIntFlowByKey(KEY_NEWEST_VERSION, 0)
            if (newestVersion > getVersionCode() && !BuildConfig.FLAVOR.contains("Lite")) {
                GameApp.instance.getWrapString(R.string.backward_version).toast()
                delay(2000)
                error(GameApp.instance.getWrapString(R.string.data_format_error))
            }
            if (newestVersion != 0 && newestVersion < 11019 && getVersionCode() >= 11019) {
                // 删除多余的event id的永久任务数据
                val clearEventIdList = repo.gameCore.getEventPool().filter { mapData[FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.DONE_EVENT.id + "_${it.id}"] != null }.map {
                    it.id
                }
                removeKeysSync(clearEventIdList.map { FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.DONE_EVENT.id + "_$it" })
                removeKeysSync(clearEventIdList.map { KEY_GAME_TASK_PROGRESS + QuestEvent.DONE_EVENT.id + "_$it" })
            }
            if (newestVersion != 0 && newestVersion < 11020 && getVersionCode() >= 11020) {
                // todo 存档兼容性处理，11016后，保存的游戏将移出云存档而独立出来，所以这里需要把它从云存档中复制到新的存档
                ContinueManager.migrate()
            }
            setIntValueByKey(KEY_NEWEST_VERSION, getVersionCode())

            // 引导
            GuideManager.guideIndex.intValue = getIntFlowByKey(KEY_GUIDE_INDEX)

            GameApp.instance.initGameSdk()
            PrivacyManager.init()

            // 游戏速度和声音设置
            getIntFlowByKey(KEY_SPEED, 1).let { speed ->
                GameSpeedManager.setSpeed(speed)
            }

            MusicManager.muteMusic = getBooleanFlowByKey(KEY_MUTE_MUSIC)
            MusicManager.muteSound = getBooleanFlowByKey(KEY_MUTE_SOUND)
            MusicManager.doMuteState()

            if (GameApp.newUser) {
                repo.gameCore.getFirstAllyIds().forEach { allyId ->
                    val ally = repo.gameCore.getAllyPool().first { it.id == allyId }
                    repo.allyManager.gain(
                        ally.copy(
                            selected = true,
                            uuid = UUID.generateUUID().toString(),
                        )
                    )
                    repo.allyManager.save()
                }
                repo.gameCore.getInitCouponPoolId().let {
                    val allyCoupon = repo.gameCore.getPoolById(it).toAward().couponAlly
                    AwardManager.couponAlly.value += allyCoupon
                }
            }
            // 游戏的初始化
            repo.doInit()
        }
    }
}