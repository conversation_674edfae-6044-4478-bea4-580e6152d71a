package com.moyu.chuanqirensheng.repository

import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.continuegame.DetailProgressManager
import com.moyu.chuanqirensheng.feature.difficult.DifficultManager
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.router.DEBUG_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.screen.effect.castSkillEffectState
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.loseBattleEffect
import com.moyu.chuanqirensheng.screen.effect.newTurnEffectState
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.turnEffect
import com.moyu.chuanqirensheng.screen.effect.winBattleEffect
import com.moyu.core.GameCore
import com.moyu.core.debug.CoreDebugConfig
import com.moyu.core.logic.battle.BattleCallback
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.model.GameSpeed
import com.moyu.core.model.environment.Environment
import com.moyu.core.model.info.BattleInfo
import com.moyu.core.model.info.BattleInfoLevel
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isBattleTree
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


val battleCallback = object : BattleCallback {
    override fun onBattleInfo(info: BattleInfo) {
        if (DebugManager.dryTest) return
        if (info.type == BattleInfoType.ExtraSkill) {
            repo.lifeInfo.add(info)
        } else {
            if (info.battleInfoLevel.value > BattleInfoLevel.DEBUG.value) {
                repo.battleInfo.add(info.copy(turn = repo.battleTurn.intValue))
                if (info.type == BattleInfoType.Dungeon) {
                    info.content.toast()
                }
            }
        }
    }

    override fun onBattleEffect(type: SoundEffect) {
        if (DebugManager.dryTest) return
        if (type != SoundEffect.DuringDialog) {
            MusicManager.playSound(type)
        } else {
            GameApp.globalScope.launch {
                repeat(12) {
                    delay(100)
                    MusicManager.playSound(type)
                }
            }
        }
    }

    override fun onToast(string: String) {
        string.toast()
    }

    override fun onTurnBegin() {
        restartEffect(newTurnEffectState, turnEffect)
    }

    override suspend fun onBattleEnd(
        gameOver: Boolean,
        turn: Int,
        allies: List<Role>,
        enemies: List<Role>
    ) {
        // 战斗结果
        if (DebugManager.debugBattle) {
            repo.inBattle.value = false
            goto(DEBUG_SCREEN)
        } else if (repo.gameMode.value.isPvp1Mode()) {
            Dialogs.skillDetailDialog.value = null
            Dialogs.allyDetailDialog.value = null
            if (gameOver) {
                PvpManager.pkFailed(allies, enemies)
            } else {
                PvpManager.pkWined(allies, enemies)
            }
        } else if (repo.gameMode.value.isPvp2Mode()) {
            Dialogs.skillDetailDialog.value = null
            Dialogs.allyDetailDialog.value = null
            if (gameOver) {
                Pvp2Manager.pkFailed(allies, enemies)
            } else {
                Pvp2Manager.pkWined(allies, enemies)
            }
        } else if (repo.gameMode.value.isTowerMode()) {
            Dialogs.skillDetailDialog.value = null
            Dialogs.allyDetailDialog.value = null
            if (gameOver) {
                GameCore.instance.onBattleEffect(SoundEffect.BattleFailed)
                Dialogs.gameLoseDialog.value = allies + enemies
                restartEffect(dialogEffectState, loseBattleEffect)
                TowerManager.failed(allies, enemies)
            } else {
                GameCore.instance.onBattleEffect(SoundEffect.BattleWin)
                Dialogs.gameWinDialog.value = allies + enemies
                restartEffect(dialogEffectState, winBattleEffect)
                TowerManager.win(allies, enemies)
            }
        } else {
            Dialogs.skillDetailDialog.value = null
            Dialogs.allyDetailDialog.value = null

            DetailProgressManager.addDefeatEnemies(enemies.filter { it.isDeath() })

            if (gameOver) {
                repo.onBattleLose(allies, enemies)
            } else {
                repo.onBattleWin(allies, enemies)
            }
        }
    }

    override suspend fun onBattleUIUpdate(battleField: BattleField) {
        repo.onBattleUpdate(battleField)
    }

    override fun extraIsGameFailed(): Boolean {
        return EventManager.extraIsGameFailed()
    }

    override fun extraIsGameWin(): Boolean {
        return EventManager.extraIsGameWin()
    }

    override fun getDebugConfig(): CoreDebugConfig {
        return DebugManager
    }

    override fun onCastSkill(skill: Skill) {
        castSkillEffectState.intValue = skill.id
    }

    override fun gameSpeed(): GameSpeed {
        return GameSpeedManager
    }

    override fun onPermanentDiff(target: Role, diff: Property) {
        BattleManager.onPermanentDiff(target, diff)
    }

    override fun onEnvironmentUpdate(environment: Environment) {
        repo.battleEnvironment.value = environment
    }

    override fun getUpgradeSkillPool(): List<Pair<String, Skill>> {
        return BattleManager.getUpgradeSkillPool()
    }

    override fun getLearnedBattleTreeSkillPool(): List<Skill> {
        return BattleManager.getGameSkills().filter { it.isBattleTree() }
    }

    override fun getCurrentDifficultLevel(): Int {
        return DifficultManager.getSelected().id
    }

    override fun canLearnSkill(skill: Skill, poolId: Int): Boolean {
        return BattleManager.canLearnMagic(skill, poolId)
    }
}