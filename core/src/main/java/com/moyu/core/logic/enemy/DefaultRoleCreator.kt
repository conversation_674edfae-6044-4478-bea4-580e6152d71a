package com.moyu.core.logic.enemy

import com.moyu.core.logic.identifier.EnemyIdentifier
import com.moyu.core.model.property.Property
import com.moyu.core.model.race.Race
import com.moyu.core.model.role.Role

object DefaultRoleCreator : RoleCreator {
    override fun create(race: Race, diffProperty: Property, extraSkills: List<Int>): Role {
        return createDetailed(race, diffProperty, EnemyIdentifier(), extraSkills)
    }
}