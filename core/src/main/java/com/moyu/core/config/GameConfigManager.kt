package com.moyu.core.config

import com.moyu.core.exception.configError
import com.moyu.core.model.Ally
import com.moyu.core.model.Arena
import com.moyu.core.model.BattlePass
import com.moyu.core.model.Buff
import com.moyu.core.model.Common
import com.moyu.core.model.DayReward
import com.moyu.core.model.Difficult
import com.moyu.core.model.DrawAward
import com.moyu.core.model.DrawItem
import com.moyu.core.model.Equipment
import com.moyu.core.model.Event
import com.moyu.core.model.GameMap
import com.moyu.core.model.Gift
import com.moyu.core.model.Mission
import com.moyu.core.model.Pool
import com.moyu.core.model.Pvp
import com.moyu.core.model.Quest
import com.moyu.core.model.Race
import com.moyu.core.model.Sell
import com.moyu.core.model.Sign
import com.moyu.core.model.Talent
import com.moyu.core.model.Title
import com.moyu.core.model.Tower
import com.moyu.core.model.TurnTable
import com.moyu.core.model.Unlock
import com.moyu.core.model.Vip
import com.moyu.core.model.WorldBoss
import com.moyu.core.model.skill.Scroll
import com.moyu.core.model.skill.Skill
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.min

class GameConfigManager : ConfigHolder {
    private val configLoaders = ConcurrentHashMap<String, List<Any>>()

    private inline fun <reified T> getPoolByKey(key: String): List<T> {
        return configLoaders[key]!! as List<T>
    }

    override fun setGameConfig(key: String, pool: List<Any>) {
        configLoaders[key] = pool
    }

    override fun getSkillPool(): List<Skill> {
        return getPoolByKey(SKILL_FILE_NAME)
    }

    override fun getBuffPool(): List<Buff> {
        return getPoolByKey(BUFF_FILE_NAME)
    }

    override fun getRacePool(): List<Race> {
        return getPoolByKey(RACE_FILE_NAME)
    }

    override fun getAllyPool(): List<Ally> {
        return getPoolByKey(ALLY_FILE_NAME)
    }

    override fun getDifficultPool(): List<Difficult> {
        return getPoolByKey(DIFFICULT_FILE_NAME)
    }

    override fun getEventPool(): List<Event> {
        return getPoolByKey(EVENT_FILE_NAME)
    }

    override fun getTalentPool(): List<Talent> {
        return getPoolByKey(TALENT_FILE_NAME)
    }

    override fun getSellPool(): List<Sell> {
        return getPoolByKey(SELL_FILE_NAME)
    }

    override fun getGameTaskPool(): List<Quest> {
        return getPoolByKey(TASK_FILE_NAME)
    }

    override fun getUnlockPool(): List<Unlock> {
        return getPoolByKey(UNLOCK_FILE_NAME)
    }

    override fun getScrollPool(): List<Scroll> {
        return getPoolByKey(SCROLL_FILE_NAME)
    }

    override fun getPoolPool(): List<Pool> {
        return getPoolByKey(POOL_FILE_NAME)
    }

    override fun getVipPool(): List<Vip> {
        return getPoolByKey(VIP_FILE_NAME)
    }

    override fun getCombinedBuffPool(): List<Buff> {
        return getPoolByKey(COMBINEDBUFF_FILE_NAME)
    }

    override fun getBattlePassPool(): List<BattlePass> {
        return getPoolByKey(WAR_PASS_FILE_NAME)
    }

    override fun getBattlePass2Pool(): List<BattlePass> {
        return getPoolByKey(WAR_PASS2_FILE_NAME)
    }

    override fun getSignPool(): List<Sign> {
        return getPoolByKey(SIGN_FILE_NAME)
    }

    override fun getMissionPool(): List<Mission> {
        return getPoolByKey(MISSION_FILE_NAME)
    }

    override fun getPvpPool(type: Int): List<Pvp> {
        return getPoolByKey<Pvp>(PVP_FILE_NAME).filter { it.type == type }
    }

    override fun getMapPool(): List<GameMap> {
        return getPoolByKey(MAP_FILE_NAME)
    }

    override fun getDrawPool(): List<DrawItem> {
        return getPoolByKey(DRAW_FILE_NAME)
    }

    override fun getDrawAwardPool(): List<DrawAward> {
        return getPoolByKey(DRAW_AWARD_FILE_NAME)
    }

    override fun getGiftPool(): List<Gift> {
        return getPoolByKey(GIFT_FILE_NAME)
    }

    override fun getDayRewardPool(): List<DayReward> {
        return getPoolByKey(DAY_REWARD_FILE_NAME)
    }

    override fun getTurnTablePool(): List<TurnTable> {
        return getPoolByKey(TURNTABLE_FILE_NAME)
    }

    override fun getTitlePool(): List<Title> {
        return getPoolByKey(POSITION_FILE_NAME)
    }

    override fun getEquipPool(): List<Equipment> {
        return getPoolByKey(EQUIPMENT_FILE_NAME)
    }

    override fun getArenaPool(): List<Arena> {
        return getPoolByKey(ARENA_FILE_NAME)
    }

    override fun getTowerPool(): List<Tower> {
        return getPoolByKey(TOWER_FILE_NAME)
    }

    override fun getWorldBossPool(): List<WorldBoss> {
        return getPoolByKey(WORLDBOSS_FILE_NAME)
    }

    override fun getSkillById(skillId: Int): Skill {
        return getSkillPool().find { it.id == skillId } ?: configError(skillId)
    }

    override fun getEquipById(equipId: Int): Equipment {
        return getEquipPool().find { it.id == equipId } ?: configError(equipId)
    }

    override fun getGameTaskById(id: Int): Quest {
        return getGameTaskPool().find { it.id == id } ?: configError(id)
    }

    override fun getAllyById(id: Int): Ally {
        return getAllyPool().find { it.id == id } ?: configError(id)
    }

    override fun getEventById(id: Int): Event {
        return getEventPool().find { it.id == id } ?: configError(id)
    }

    override fun getScrollById(id: Int): Scroll {
        return getScrollPool().find { it.id == id } ?: configError(id)
    }

    override fun getBuffById(buffId: Int): Buff {
        return getBuffPool().find { it.id == buffId } ?: configError(buffId)
    }

    override fun getRaceById(raceId: Int): Race {
        return getRacePool().find { it.id == raceId } ?: configError(raceId)
    }

    override fun getTalentById(id: Int): Talent {
        return getTalentPool().find { it.id == id } ?: configError(id)
    }

    override fun getUnlockById(id: Int): Unlock {
        return getUnlockPool().find { it.id == id } ?: configError(id)
    }

    override fun getPoolById(id: Int): Pool {
        return getPoolPool().firstOrNull { it.id == id } ?: configError(id)
    }

    override fun getPoolByKeyAny(key: String): List<Any> {
        return configLoaders[key]!!
    }

    override fun getConstA(): Double {
        return 10.0
    }

    override fun getConstB(): Double {
        return 5.0
    }

    override fun getFirstAllyIds(): List<Int> {
        return getValueById(51).split(",").map { it.toInt() }
    }

    override fun getFirstEndingAwardPoolId(): Int {
        return getValueById(69).toInt()
    }

    override fun getInitGold(): Int {
        return getValueById(54).toInt()
    }

    override fun getInitStone(): Int {
        return getValueById(81).toInt()
    }

    override fun getInitWood(): Int {
        return getValueById(76).toInt()
    }

    override fun getEndingAwardLevel(age: Int): Int {
        val requiredAge = getValueById(8).split(",").map { it.toInt() }
        return requiredAge.indexOfLast { it <= age }
    }

    override fun getEndingDiamondLevel(age: Int): Int {
        val requiredAge = getValueById(9).split(",").map { it.toInt() }
        return requiredAge.indexOfLast { it <= age }
    }

    override fun getEndingAwardDiamond(difficult: Int, level: Int): Int {
        val diamondList = getValueById(101 + difficult).split(",").map { it.toInt() }
        return diamondList[level]
    }

    override fun getQuality1AllyNum(difficult: Int, level: Int): Int {
        return getValueById(1001 + difficult).split(",").map { it.toInt() }[level]
    }

    override fun getQuality2AllyNum(difficult: Int, level: Int): Int {
        return getValueById(1011 + difficult).split(",").map { it.toInt() }[level]
    }

    override fun getQuality3AllyNum(difficult: Int, level: Int): Int {
        return getValueById(1021 + difficult).split(",").map { it.toInt() }[level]
    }

    override fun getQuality4AllyNum(difficult: Int, level: Int): Int {
        return getValueById(1031 + difficult).split(",").map { it.toInt() }[level]
    }

    override fun getQuality5AllyNum(difficult: Int, level: Int): Int {
        return getValueById(1041 + difficult).split(",").map { it.toInt() }[level]
    }

    override fun getQuality1AllyPool(): List<Int> {
        return getValueById(1101).split(",").map { it.toInt() }
    }

    override fun getQuality2AllyPool(): List<Int> {
        return getValueById(1102).split(",").map { it.toInt() }
    }

    override fun getQuality3AllyPool(): List<Int> {
        return getValueById(1103).split(",").map { it.toInt() }
    }

    override fun getQuality4AllyPool(): List<Int> {
        return getValueById(1104).split(",").map { it.toInt() }
    }

    override fun getQuality5AllyPool(): List<Int> {
        return getValueById(1105).split(",").map { it.toInt() }
    }

    override fun getRefreshShopCost(): Int {
        return getValueById(3).toInt()
    }

    override fun getKeyToDiamondRate(): Int {
        return getValueById(4).toInt()
    }

    override fun getDailyShopRefreshCount(): Int {
        return getValueById(2).toInt()
    }

    override fun getWarPassQuestCount(): Int {
        return getValueById(6).toInt()
    }

    override fun getNewQuestCount(): Int {
        return getValueById(7).toInt()
    }

    override fun getDailyQuestCount(): Int {
        return getValueById(1).toInt()
    }

    override fun getMaxOtherUseYourCount(): Int {
        return getValueById(52).toInt()
    }

    override fun getMaxUseOtherCount(): Int {
        return getValueById(53).toInt()
    }

    override fun getShareCodeAwardKeyNum(): Int {
        return getValueById(78).toInt()
    }

    override fun getMaxOneDayDiamondLimit(): Int {
        // todo 防止被简单篡改配置，内存加一个数
        return min(1500, getValueById(66).toInt())
    }

    override fun getImageShareAwardNum(): Int {
        return getValueById(72).toInt()
    }

    override fun getTextShareAwardNum(): Int {
        return getValueById(71).toInt()
    }

    override fun getShopDataByIndex(index: Int): List<Int> {
        return getValueById(55 + index).split(",").map { it.toInt() }
    }

    override fun getUnlockTalentPageLevel(): Int {
        return getValueById(77).toInt()
    }

    override fun getFamousDiamond(): Int {
        return getValueById(80).toInt()
    }

    override fun getAllyCouponRate(): Int {
        return getValueById(82).toInt()
    }

    override fun getHeroCouponRate(): Int {
        return getValueById(83).toInt()
    }

    override fun getInitCouponPoolId(): Int {
        return getValueById(84).toInt()
    }

    override fun getCheapLotteryCosts(): List<Int> {
        return getValueById(2101).split(",").map { it.toInt() }
    }

    override fun getExpensiveLotteryCosts(): List<Int> {
        return getValueById(2102).split(",").map { it.toInt() }
    }

    override fun getHolidayLotteryCosts(): List<Int> {
        return getValueById(2103).split(",").map { it.toInt() }
    }

    override fun getTowerAwardKey(): Int {
        return getValueById(86).toInt()
    }

    override fun getGiftById(value: Int): Gift? {
        return getGiftPool().find { it.id == value }
    }

    override fun getWorldBossById(id: Int): WorldBoss? {
        return getWorldBossPool().find { it.id == id }
    }

    override fun getWorldBossByDay(day: Int): WorldBoss? {
        return getWorldBossPool().find { it.day == day }
    }

    private fun getValueById(id: Int) = getPoolByKey<Common>(COMMON_FILE_NAME).first { it.id == id }.num
}